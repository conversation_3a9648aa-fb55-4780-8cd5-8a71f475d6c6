import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { handleError } from './lib/error-handler';

export async function middleware(request: NextRequest) {
  try {
    const { pathname } = request.nextUrl;

    // List of protected routes that require authentication
    const protectedRoutes = [
      '/admin',
      '/dashboard',
      '/properties',
      '/reports',
      '/settings'
    ];

    // Check if the current path is a protected route
    const isProtectedRoute = protectedRoutes.some(route =>
      pathname.startsWith(route)
    );

    // Add security headers
    const response = NextResponse.next();

    // Add security headers
    response.headers.set('X-DNS-Prefetch-Control', 'on');
    response.headers.set('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('X-Frame-Options', 'SAMEORIGIN');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Referrer-Policy', 'origin-when-cross-origin');

    // Force dynamic rendering for protected routes
    if (isProtectedRoute) {
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      response.headers.set('x-middleware-cache', 'no-cache');
    }

    return response;
  } catch (error) {
    return handleError(error);
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}; 