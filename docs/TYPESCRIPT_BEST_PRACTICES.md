# TypeScript Best Practices for AI Assistant Compatibility

This document outlines TypeScript best practices that make the codebase more compatible with AI assistants like Augment Code.

## 1. Strict Type Safety

### Use Strict TypeScript Configuration
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "useUnknownInCatchVariables": true,
    "alwaysStrict": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true
  }
}
```

### Avoid `any` Type
```typescript
// ❌ Bad
function processData(data: any) {
  return data.someProperty;
}

// ✅ Good
interface DataType {
  someProperty: string;
}

function processData(data: DataType) {
  return data.someProperty;
}
```

## 2. Safe Function Calls

### Use Optional Chaining
```typescript
// ❌ Bad - Can cause "Cannot read properties of undefined"
const result = obj.method.call(context, arg);

// ✅ Good
const result = obj?.method?.call(context, arg);
```

### Use Type Guards
```typescript
function isCallable(obj: any): obj is { call: Function } {
  return obj && typeof obj.call === 'function';
}

if (isCallable(someObject.method)) {
  someObject.method.call(context, arg);
}
```

### Use Safe Utility Functions
```typescript
import { safeCall, safeGet } from '@/lib/utils/type-safety';

// Safe method calling
const result = safeCall(obj, 'method', context, arg1, arg2);

// Safe property access
const value = safeGet(obj, 'property');
```

## 3. Proper Error Handling

### Use Try-Catch with Typed Errors
```typescript
try {
  await riskyOperation();
} catch (error) {
  if (error instanceof Error) {
    console.error('Operation failed:', error.message);
  } else {
    console.error('Unknown error:', error);
  }
}
```

### Use Error Boundaries in React
```typescript
<ErrorBoundary fallback={ErrorFallback}>
  <MyComponent />
</ErrorBoundary>
```

## 4. Defensive Programming

### Always Check for Null/Undefined
```typescript
// ❌ Bad
function processUser(user: User | null) {
  return user.name.toUpperCase();
}

// ✅ Good
function processUser(user: User | null) {
  if (!user?.name) {
    return 'Unknown User';
  }
  return user.name.toUpperCase();
}
```

### Use Default Values
```typescript
function createConfig(options: Partial<Config> = {}) {
  return {
    timeout: 5000,
    retries: 3,
    ...options
  };
}
```

## 5. React Component Safety

### Proper Props Typing
```typescript
interface ComponentProps {
  title: string;
  onClick?: () => void;
  children?: React.ReactNode;
}

function MyComponent({ title, onClick, children }: ComponentProps) {
  const handleClick = useCallback(() => {
    onClick?.();
  }, [onClick]);

  return (
    <div onClick={handleClick}>
      <h1>{title}</h1>
      {children}
    </div>
  );
}
```

### Safe Event Handlers
```typescript
const handleSubmit = useCallback((event: React.FormEvent) => {
  event.preventDefault();
  
  try {
    onSubmit?.(formData);
  } catch (error) {
    console.error('Submit failed:', error);
    setError('Submission failed. Please try again.');
  }
}, [formData, onSubmit]);
```

## 6. API and Async Safety

### Proper Response Typing
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

async function fetchData<T>(url: string): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      return { success: false, error: 'Network error' };
    }
    
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}
```

## 7. Type Utilities

### Create Reusable Type Guards
```typescript
export function isDefined<T>(value: T | null | undefined): value is T {
  return value != null;
}

export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

export function hasProperty<T, K extends string>(
  obj: T,
  prop: K
): obj is T & Record<K, unknown> {
  return obj != null && prop in obj;
}
```

## 8. Testing Considerations

### Type-Safe Test Utilities
```typescript
export function createMockProperty(overrides: Partial<Property> = {}): Property {
  return {
    id: 'test-id',
    address: 'Test Address',
    city: 'Test City',
    state: 'CA',
    zipCode: '90210',
    // ... other required properties
    ...overrides
  };
}
```

## 9. Build and Development

### Use Type Checking in CI/CD
```json
{
  "scripts": {
    "prebuild": "npm run type-check && npm run lint",
    "type-check": "tsc --noEmit",
    "type-check:watch": "tsc --noEmit --watch"
  }
}
```

### ESLint Configuration
```javascript
module.exports = {
  rules: {
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/strict-boolean-expressions": "error",
    "@typescript-eslint/prefer-nullish-coalescing": "error",
    "@typescript-eslint/prefer-optional-chain": "error"
  }
};
```

## 10. AI Assistant Compatibility

### Clear Function Signatures
```typescript
// ✅ Clear and descriptive
async function evaluatePropertyForDevelopment(
  property: Property,
  criteria: EvaluationCriteria
): Promise<EvaluationResults> {
  // Implementation
}
```

### Comprehensive JSDoc Comments
```typescript
/**
 * Evaluates a property against development criteria
 * @param property - The property to evaluate
 * @param criteria - The evaluation criteria to apply
 * @returns Promise resolving to evaluation results
 * @throws {Error} When property data is invalid
 */
async function evaluateProperty(
  property: Property,
  criteria: EvaluationCriteria
): Promise<EvaluationResults> {
  // Implementation
}
```

### Consistent Naming Conventions
- Use descriptive variable names
- Follow camelCase for variables and functions
- Use PascalCase for types and interfaces
- Use UPPER_CASE for constants

By following these practices, the codebase becomes more predictable, safer, and easier for AI assistants to understand and work with.
