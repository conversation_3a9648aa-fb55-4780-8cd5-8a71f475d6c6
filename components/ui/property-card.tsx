'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Property } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MapPin, 
  Building, 
  DollarSign, 
  Ruler, 
  Star,
  Eye,
  GitCompare,
  Download,
  Share,
  CheckCircle,
  XCircle,
  AlertTriangle,
  TrendingUp,
  Calendar,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface PropertyCardProps {
  property: Property;
  showEvaluation?: boolean;
  showActions?: boolean;
  onCompare?: (property: Property) => void;
  onSave?: (property: Property) => void;
  onShare?: (property: Property) => void;
  onClick?: (property: Property) => void;
  isSelected?: boolean;
  isComparing?: boolean;
  className?: string;
}

export function PropertyCard({
  property,
  showEvaluation = true,
  showActions = true,
  onCompare,
  onSave,
  onShare,
  onClick,
  isSelected = false,
  isComparing = false,
  className,
}: PropertyCardProps) {
  const [imageError, setImageError] = useState(false);
  
  const evaluation = property.evaluationResults;
  const hasEvaluation = evaluation && showEvaluation;

  // Get evaluation status
  const getEvaluationStatus = () => {
    if (!evaluation) return null;
    
    if (evaluation.passed) {
      return {
        icon: CheckCircle,
        color: 'text-green-500',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        label: 'Meets Criteria',
      };
    } else if (evaluation.overallScore >= 70) {
      return {
        icon: AlertTriangle,
        color: 'text-yellow-500',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200',
        label: 'Partial Match',
      };
    } else {
      return {
        icon: XCircle,
        color: 'text-red-500',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        label: 'Does Not Meet',
      };
    }
  };

  const evaluationStatus = getEvaluationStatus();

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format area
  const formatArea = (sqft: number) => {
    return new Intl.NumberFormat('en-US').format(sqft);
  };

  return (
    <Card
      className={cn(
        'group hover:shadow-lg transition-all duration-200',
        isSelected && 'ring-2 ring-blue-500',
        isComparing && 'ring-2 ring-purple-500',
        evaluationStatus && hasEvaluation && evaluationStatus.borderColor,
        onClick && 'cursor-pointer',
        className
      )}
      onClick={() => onClick?.(property)}
    >
      {/* Property Image */}
      <div className="relative h-48 overflow-hidden rounded-t-lg">
        {!imageError ? (
          <img
            src={property.imageUrl || '/images/property-placeholder.jpg'}
            alt={property.address}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            <Building className="h-12 w-12 text-gray-400" />
          </div>
        )}
        
        {/* Evaluation Status Badge */}
        {evaluationStatus && hasEvaluation && (
          <div className={cn(
            'absolute top-3 right-3 px-2 py-1 rounded-full text-xs font-medium',
            evaluationStatus.bgColor,
            evaluationStatus.color
          )}>
            <evaluationStatus.icon className="inline h-3 w-3 mr-1" />
            {evaluationStatus.label}
          </div>
        )}

        {/* Property Type Badge */}
        <div className="absolute top-3 left-3">
          <Badge variant="secondary" className="bg-white/90 text-gray-700">
            {property.propertyType || 'Residential'}
          </Badge>
        </div>

        {/* Quick Actions Overlay */}
        {showActions && (
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
            <Link href={`/properties/${property.id}`}>
              <Button size="sm" variant="secondary">
                <Eye className="h-4 w-4 mr-1" />
                View
              </Button>
            </Link>
            {onCompare && (
              <Button 
                size="sm" 
                variant="secondary"
                onClick={() => onCompare(property)}
              >
                <GitCompare className="h-4 w-4 mr-1" />
                Compare
              </Button>
            )}
          </div>
        )}
      </div>

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg leading-tight mb-1">
              {property.address}
            </CardTitle>
            <CardDescription className="flex items-center text-sm">
              <MapPin className="h-3 w-3 mr-1" />
              {property.city}, {property.state} {property.zipCode}
            </CardDescription>
          </div>
          
          {/* Overall Score */}
          {hasEvaluation && evaluation.overallScore && (
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-600">
                {evaluation.overallScore}
              </div>
              <div className="text-xs text-gray-500">Score</div>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Property Details */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-gray-400" />
            <div>
              <div className="font-medium">
                {formatCurrency(property.assessedValue)}
              </div>
              <div className="text-xs text-gray-500">Assessed Value</div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Ruler className="h-4 w-4 text-gray-400" />
            <div>
              <div className="font-medium">
                {formatArea(property.lotSize)} sq ft
              </div>
              <div className="text-xs text-gray-500">Lot Size</div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Building className="h-4 w-4 text-gray-400" />
            <div>
              <div className="font-medium">{property.zoning}</div>
              <div className="text-xs text-gray-500">Zoning</div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <div>
              <div className="font-medium">
                {property.yearBuilt || 'N/A'}
              </div>
              <div className="text-xs text-gray-500">Year Built</div>
            </div>
          </div>
        </div>

        {/* Evaluation Highlights */}
        {hasEvaluation && evaluation.criteria && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Key Criteria</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-1">
                {evaluation.criteria.qctDdaStatus ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <XCircle className="h-3 w-3 text-red-500" />
                )}
                <span>QCT/DDA Status</span>
              </div>
              
              <div className="flex items-center gap-1">
                {evaluation.criteria.densityPotential?.meets250Requirement ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <XCircle className="h-3 w-3 text-red-500" />
                )}
                <span>250+ Units</span>
              </div>
              
              <div className="flex items-center gap-1">
                {evaluation.criteria.transitAccess?.meetsRequirements ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <XCircle className="h-3 w-3 text-red-500" />
                )}
                <span>Transit Access</span>
              </div>
              
              <div className="flex items-center gap-1">
                {evaluation.criteria.lotSizeAnalysis?.isIdeal || evaluation.criteria.lotSizeAnalysis?.isAcceptable ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <XCircle className="h-3 w-3 text-red-500" />
                )}
                <span>Lot Size</span>
              </div>
            </div>
          </div>
        )}

        {/* Development Potential */}
        {hasEvaluation && evaluation.criteria?.densityPotential && (
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">
                Development Potential
              </span>
            </div>
            <div className="text-sm text-blue-700">
              <div>Base Units: {evaluation.criteria.densityPotential.baseUnits}</div>
              <div>With Bonuses: {evaluation.criteria.densityPotential.totalUnits}</div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {showActions && (
          <div className="flex gap-2 pt-2 border-t">
            <Link href={`/properties/${property.id}`} className="flex-1">
              <Button variant="outline" size="sm" className="w-full">
                <Eye className="h-4 w-4 mr-1" />
                Details
              </Button>
            </Link>
            
            {onCompare && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => onCompare(property)}
                className={cn(
                  isComparing && 'bg-purple-50 border-purple-200 text-purple-700'
                )}
              >
                <GitCompare className="h-4 w-4 mr-1" />
                {isComparing ? 'Added' : 'Compare'}
              </Button>
            )}
            
            {onSave && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => onSave(property)}
              >
                <Star className="h-4 w-4" />
              </Button>
            )}
            
            {onShare && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => onShare(property)}
              >
                <Share className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}

        {/* Confidence Score */}
        {hasEvaluation && evaluation.confidenceScore && (
          <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
            <span>Analysis Confidence</span>
            <div className="flex items-center gap-1">
              <Zap className="h-3 w-3" />
              <span>{evaluation.confidenceScore}%</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
