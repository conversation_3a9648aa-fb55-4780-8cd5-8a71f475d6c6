'use client';

import { useState, useEffect } from 'react';
import { Property } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  MapPin, 
  Building, 
  DollarSign, 
  Ruler, 
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  Eye,
  Download,
  Share,
  Star,
  TrendingUp,
  Clock,
  Zap,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface PropertyPreviewProps {
  property: Property | null;
  loading?: boolean;
  onAnalyze?: (property: Property) => void;
  onSave?: (property: Property) => void;
  onShare?: (property: Property) => void;
  className?: string;
}

export function PropertyPreview({
  property,
  loading = false,
  onAnalyze,
  onSave,
  onShare,
  className,
}: PropertyPreviewProps) {
  const [imageError, setImageError] = useState(false);
  
  if (loading) {
    return (
      <Card className={cn('animate-pulse', className)}>
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="h-48 bg-gray-200 rounded"></div>
            <div className="grid grid-cols-2 gap-4">
              <div className="h-16 bg-gray-200 rounded"></div>
              <div className="h-16 bg-gray-200 rounded"></div>
            </div>
            <div className="h-8 bg-gray-200 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!property) {
    return (
      <Card className={cn('border-dashed border-2', className)}>
        <CardContent className="flex flex-col items-center justify-center py-12 text-center">
          <Building className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Property Selected
          </h3>
          <p className="text-gray-500 max-w-sm">
            Enter a property address above to see detailed information and analysis results.
          </p>
        </CardContent>
      </Card>
    );
  }

  const evaluation = property.evaluationResults;
  const hasEvaluation = !!evaluation;

  // Get evaluation status
  const getEvaluationStatus = () => {
    if (!evaluation) return null;
    
    if (evaluation.passed) {
      return {
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        label: 'Meets All Criteria',
        description: 'This property meets all development requirements',
      };
    } else if (evaluation.overallScore >= 70) {
      return {
        icon: AlertTriangle,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200',
        label: 'Partial Match',
        description: 'This property meets most requirements with some exceptions',
      };
    } else {
      return {
        icon: XCircle,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        label: 'Does Not Meet Criteria',
        description: 'This property does not meet key development requirements',
      };
    }
  };

  const evaluationStatus = getEvaluationStatus();

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format area
  const formatArea = (sqft: number) => {
    return new Intl.NumberFormat('en-US').format(sqft);
  };

  return (
    <Card className={cn(
      'overflow-hidden',
      evaluationStatus && hasEvaluation && evaluationStatus.borderColor,
      className
    )}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-xl leading-tight mb-2">
              {property.address}
            </CardTitle>
            <CardDescription className="flex items-center text-base">
              <MapPin className="h-4 w-4 mr-1" />
              {property.city}, {property.state} {property.zipCode}
            </CardDescription>
          </div>
          
          {/* Overall Score */}
          {hasEvaluation && evaluation.overallScore && (
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-1">
                {evaluation.overallScore}
              </div>
              <div className="text-sm text-gray-500">Overall Score</div>
            </div>
          )}
        </div>

        {/* Evaluation Status Alert */}
        {evaluationStatus && hasEvaluation && (
          <Alert className={cn(evaluationStatus.bgColor, evaluationStatus.borderColor)}>
            <evaluationStatus.icon className={cn('h-4 w-4', evaluationStatus.color)} />
            <AlertDescription className={evaluationStatus.color}>
              <strong>{evaluationStatus.label}</strong> - {evaluationStatus.description}
            </AlertDescription>
          </Alert>
        )}
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Property Image */}
        <div className="relative h-64 overflow-hidden rounded-lg">
          {!imageError ? (
            <img
              src={property.imageUrl || '/images/property-placeholder.jpg'}
              alt={property.address}
              className="w-full h-full object-cover"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <Building className="h-16 w-16 text-gray-400" />
            </div>
          )}
          
          {/* Property Type Badge */}
          <div className="absolute top-4 left-4">
            <Badge variant="secondary" className="bg-white/90 text-gray-700">
              {property.propertyType || 'Residential'}
            </Badge>
          </div>
        </div>

        {/* Basic Property Information */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <DollarSign className="h-6 w-6 text-gray-400 mx-auto mb-2" />
            <div className="font-semibold text-lg">
              {formatCurrency(property.assessedValue)}
            </div>
            <div className="text-sm text-gray-500">Assessed Value</div>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <Ruler className="h-6 w-6 text-gray-400 mx-auto mb-2" />
            <div className="font-semibold text-lg">
              {formatArea(property.lotSize)}
            </div>
            <div className="text-sm text-gray-500">Lot Size (sq ft)</div>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <Building className="h-6 w-6 text-gray-400 mx-auto mb-2" />
            <div className="font-semibold text-lg">{property.zoning}</div>
            <div className="text-sm text-gray-500">Zoning</div>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <Clock className="h-6 w-6 text-gray-400 mx-auto mb-2" />
            <div className="font-semibold text-lg">
              {property.yearBuilt || 'N/A'}
            </div>
            <div className="text-sm text-gray-500">Year Built</div>
          </div>
        </div>

        {/* Evaluation Results */}
        {hasEvaluation && evaluation.criteria && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Evaluation Results</h3>
            
            {/* Key Criteria Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <h4 className="font-medium text-gray-700">Location & Zoning</h4>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm">QCT/DDA Status</span>
                  {evaluation.criteria.qctDdaStatus ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm">Neighborhood Change Zone</span>
                  {evaluation.criteria.neighborhoodChangeZone ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm">Lot Size Requirements</span>
                  {evaluation.criteria.lotSizeAnalysis?.isIdeal || evaluation.criteria.lotSizeAnalysis?.isAcceptable ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-medium text-gray-700">Development Potential</h4>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm">250+ Units Potential</span>
                  {evaluation.criteria.densityPotential?.meets250Requirement ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm">Height Requirements (65+ ft)</span>
                  {evaluation.criteria.heightRestrictions?.meetsRequirement ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm">Transit Access</span>
                  {evaluation.criteria.transitAccess?.meetsRequirements ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
              </div>
            </div>

            {/* Development Potential Details */}
            {evaluation.criteria.densityPotential && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-3">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-blue-800">
                    Development Potential Analysis
                  </span>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <div className="text-blue-700 font-medium">
                      {evaluation.criteria.densityPotential.baseUnits}
                    </div>
                    <div className="text-blue-600">Base Units</div>
                  </div>
                  <div>
                    <div className="text-blue-700 font-medium">
                      {evaluation.criteria.densityPotential.bonusUnits}
                    </div>
                    <div className="text-blue-600">Bonus Units</div>
                  </div>
                  <div>
                    <div className="text-blue-700 font-medium text-lg">
                      {evaluation.criteria.densityPotential.totalUnits}
                    </div>
                    <div className="text-blue-600">Total Units</div>
                  </div>
                </div>
              </div>
            )}

            {/* Recommendations */}
            {evaluation.recommendations && evaluation.recommendations.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-gray-700">Recommendations</h4>
                <div className="space-y-2">
                  {evaluation.recommendations.slice(0, 3).map((recommendation, index) => (
                    <div key={index} className="flex items-start gap-2 p-3 bg-yellow-50 rounded-lg">
                      <Info className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-yellow-800">{recommendation}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Confidence Score */}
            {evaluation.confidenceScore && (
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium">Analysis Confidence</span>
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">{evaluation.confidenceScore}%</span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t">
          {!hasEvaluation && onAnalyze && (
            <Button onClick={() => onAnalyze(property)} className="flex-1">
              <Zap className="h-4 w-4 mr-2" />
              Analyze Property
            </Button>
          )}
          
          {hasEvaluation && (
            <>
              <Button asChild className="flex-1">
                <a href={`/properties/${property.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Full Report
                </a>
              </Button>
              
              <Button variant="outline" onClick={() => onSave?.(property)}>
                <Star className="h-4 w-4 mr-2" />
                Save
              </Button>
              
              <Button variant="outline" onClick={() => onShare?.(property)}>
                <Share className="h-4 w-4 mr-2" />
                Share
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
