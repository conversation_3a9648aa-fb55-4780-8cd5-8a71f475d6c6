'use client';

import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { useState, useEffect } from 'react';

function AuthenticatedHeaderContent() {
  const { data: session, status } = useSession();

  return (
    <div className="flex items-center space-x-4">
      {status === 'loading' ? (
        <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      ) : status === 'authenticated' && session ? (
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600 dark:text-gray-300">
            {session.user?.name}
          </span>
          <button
            onClick={() => signOut()}
            className="text-sm text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
          >
            Sign Out
          </button>
        </div>
      ) : (
        <Link
          href="/auth/signin"
          className="text-sm text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
        >
          Sign In
        </Link>
      )}
    </div>
  );
}

function HeaderContent() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <header className="bg-white dark:bg-gray-900 shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">RE</span>
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                Real Estate Automation
              </span>
            </Link>
          </div>

          {mounted ? (
            <AuthenticatedHeaderContent />
          ) : (
            <div className="flex items-center space-x-4">
              <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}

export function SimpleHeader() {
  return <HeaderContent />;
}
