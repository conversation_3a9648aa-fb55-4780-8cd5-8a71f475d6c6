'use client';

import { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Search, 
  MapPin, 
  Upload, 
  X, 
  CheckCircle, 
  AlertTriangle,
  Loader2 
} from 'lucide-react';
import toast from 'react-hot-toast';

// Property input validation schema
const propertyInputSchema = z.object({
  address: z.string().min(5, 'Address must be at least 5 characters'),
  parcelId: z.string().optional(),
  city: z.string().min(2, 'City is required'),
  state: z.string().min(2, 'State is required'),
  zipCode: z.string().regex(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code format'),
  searchRadius: z.number().min(0.1).max(50).default(5),
});

type PropertyInputData = z.infer<typeof propertyInputSchema>;

interface PropertyInputFormProps {
  onSubmit: (data: PropertyInputData & { coordinates?: { lat: number; lng: number } }) => void;
  onBatchUpload?: (properties: PropertyInputData[]) => void;
  loading?: boolean;
  className?: string;
}

interface AddressSuggestion {
  description: string;
  place_id: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
}

export function PropertyInputForm({ 
  onSubmit, 
  onBatchUpload, 
  loading = false,
  className = '' 
}: PropertyInputFormProps) {
  const [addressSuggestions, setAddressSuggestions] = useState<AddressSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [geocoding, setGeocoding] = useState(false);
  const [showBatchUpload, setShowBatchUpload] = useState(false);
  const [batchFile, setBatchFile] = useState<File | null>(null);
  const [batchProcessing, setBatchProcessing] = useState(false);
  
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const geocoder = useRef<google.maps.Geocoder | null>(null);
  const addressInputRef = useRef<HTMLInputElement>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<PropertyInputData>({
    resolver: zodResolver(propertyInputSchema),
    defaultValues: {
      searchRadius: 5,
    },
  });

  const addressValue = watch('address');

  // Initialize Google Maps services
  useEffect(() => {
    if (typeof window !== 'undefined' && window.google) {
      autocompleteService.current = new google.maps.places.AutocompleteService();
      geocoder.current = new google.maps.Geocoder();
    }
  }, []);

  // Handle address autocomplete
  useEffect(() => {
    if (!addressValue || addressValue.length < 3 || !autocompleteService.current) {
      setAddressSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    const timer = setTimeout(() => {
      autocompleteService.current?.getPlacePredictions(
        {
          input: addressValue,
          types: ['address'],
          componentRestrictions: { country: 'us' },
        },
        (predictions, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
            setAddressSuggestions(predictions);
            setShowSuggestions(true);
          } else {
            setAddressSuggestions([]);
            setShowSuggestions(false);
          }
        }
      );
    }, 300);

    return () => clearTimeout(timer);
  }, [addressValue]);

  // Handle address selection from suggestions
  const handleAddressSelect = async (suggestion: AddressSuggestion) => {
    setValue('address', suggestion.description);
    setShowSuggestions(false);
    
    // Auto-fill city, state, zip from the selected address
    const addressComponents = suggestion.description.split(', ');
    if (addressComponents.length >= 3) {
      const lastComponent = addressComponents[addressComponents.length - 1];
      const secondLastComponent = addressComponents[addressComponents.length - 2];

      if (lastComponent && secondLastComponent) {
        const stateZip = lastComponent.split(' ');
        setValue('city', secondLastComponent);
        if (stateZip[0]) {
          setValue('state', stateZip[0]);
        }
        if (stateZip[1]) {
          setValue('zipCode', stateZip[1]);
        }
      }
    }

    // Geocode the address
    if (geocoder.current) {
      setGeocoding(true);
      try {
        const results = await new Promise<google.maps.GeocoderResult[]>((resolve, reject) => {
          geocoder.current!.geocode(
            { placeId: suggestion.place_id },
            (results, status) => {
              if (status === google.maps.GeocoderStatus.OK && results) {
                resolve(results);
              } else {
                reject(new Error('Geocoding failed'));
              }
            }
          );
        });

        if (results[0]) {
          const location = results[0].geometry.location;
          // Store coordinates for later use
          (window as any).selectedPropertyCoordinates = {
            lat: location.lat(),
            lng: location.lng(),
          };
        }
      } catch (error) {
        console.error('Geocoding error:', error);
        toast.error('Failed to get coordinates for the address');
      } finally {
        setGeocoding(false);
      }
    }
  };

  // Handle form submission
  const handleFormSubmit = (data: PropertyInputData) => {
    const coordinates = (window as any).selectedPropertyCoordinates;
    onSubmit({ ...data, coordinates });
  };

  // Handle CSV file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'text/csv') {
      setBatchFile(file);
    } else {
      toast.error('Please select a valid CSV file');
    }
  };

  // Process batch CSV upload
  const processBatchUpload = async () => {
    if (!batchFile || !onBatchUpload) return;

    setBatchProcessing(true);
    try {
      const text = await batchFile.text();
      const lines = text.split('\n').filter(line => line.trim());

      if (lines.length === 0) {
        throw new Error('CSV file is empty');
      }

      const firstLine = lines[0];
      if (!firstLine) {
        throw new Error('CSV file has no header row');
      }

      const headers = firstLine.split(',').map(h => h.trim().toLowerCase());
      const properties: PropertyInputData[] = [];

      for (let i = 1; i < lines.length; i++) {
        const currentLine = lines[i];
        if (!currentLine) continue;

        const values = currentLine.split(',').map(v => v.trim());
        const property: any = {};
        
        headers.forEach((header, index) => {
          if (values[index]) {
            switch (header) {
              case 'address':
                property.address = values[index];
                break;
              case 'parcel_id':
              case 'parcelid':
                property.parcelId = values[index];
                break;
              case 'city':
                property.city = values[index];
                break;
              case 'state':
                property.state = values[index];
                break;
              case 'zip':
              case 'zipcode':
              case 'zip_code':
                property.zipCode = values[index];
                break;
              case 'radius':
              case 'search_radius':
                property.searchRadius = parseFloat(values[index]) || 5;
                break;
            }
          }
        });
        
        // Validate required fields
        if (property.address && property.city && property.state && property.zipCode) {
          properties.push(property);
        }
      }
      
      if (properties.length > 0) {
        onBatchUpload(properties);
        toast.success(`Successfully processed ${properties.length} properties`);
        setBatchFile(null);
        setShowBatchUpload(false);
      } else {
        toast.error('No valid properties found in the CSV file');
      }
    } catch (error) {
      console.error('Batch upload error:', error);
      toast.error('Failed to process CSV file');
    } finally {
      setBatchProcessing(false);
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="h-5 w-5" />
          Property Search
        </CardTitle>
        <CardDescription>
          Enter property details to begin analysis. Use address autocomplete for accurate results.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Single Property Form */}
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          {/* Address Input with Autocomplete */}
          <div className="relative">
            <label className="block text-sm font-medium mb-2">
              Property Address *
            </label>
            <div className="relative">
              <Input
                {...register('address')}
                ref={addressInputRef}
                placeholder="123 Main Street, Los Angeles, CA 90210"
                className="pr-10"
                autoComplete="off"
              />
              {geocoding && (
                <Loader2 className="absolute right-3 top-3 h-4 w-4 animate-spin text-gray-400" />
              )}
            </div>
            
            {/* Address Suggestions Dropdown */}
            {showSuggestions && addressSuggestions.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                {addressSuggestions.map((suggestion, index) => (
                  <button
                    key={suggestion.place_id}
                    type="button"
                    className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
                    onClick={() => handleAddressSelect(suggestion)}
                  >
                    <div className="font-medium text-sm">
                      {suggestion.structured_formatting.main_text}
                    </div>
                    <div className="text-xs text-gray-500">
                      {suggestion.structured_formatting.secondary_text}
                    </div>
                  </button>
                ))}
              </div>
            )}
            
            {errors.address && (
              <p className="text-red-500 text-sm mt-1">{errors.address.message}</p>
            )}
          </div>

          {/* Parcel ID (Optional) */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Parcel ID (Optional)
            </label>
            <Input
              {...register('parcelId')}
              placeholder="APN: 1234-567-890"
            />
            {errors.parcelId && (
              <p className="text-red-500 text-sm mt-1">{errors.parcelId.message}</p>
            )}
          </div>

          {/* City, State, ZIP Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">City *</label>
              <Input
                {...register('city')}
                placeholder="Los Angeles"
              />
              {errors.city && (
                <p className="text-red-500 text-sm mt-1">{errors.city.message}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">State *</label>
              <Input
                {...register('state')}
                placeholder="CA"
                maxLength={2}
              />
              {errors.state && (
                <p className="text-red-500 text-sm mt-1">{errors.state.message}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">ZIP Code *</label>
              <Input
                {...register('zipCode')}
                placeholder="90210"
              />
              {errors.zipCode && (
                <p className="text-red-500 text-sm mt-1">{errors.zipCode.message}</p>
              )}
            </div>
          </div>

          {/* Search Radius */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Search Radius (miles)
            </label>
            <Input
              {...register('searchRadius', { valueAsNumber: true })}
              type="number"
              min="0.1"
              max="50"
              step="0.1"
              placeholder="5"
            />
            {errors.searchRadius && (
              <p className="text-red-500 text-sm mt-1">{errors.searchRadius.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex gap-3">
            <Button 
              type="submit" 
              disabled={loading || geocoding}
              className="flex-1"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Analyze Property
                </>
              )}
            </Button>
            
            {onBatchUpload && (
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowBatchUpload(!showBatchUpload)}
              >
                <Upload className="mr-2 h-4 w-4" />
                Batch Upload
              </Button>
            )}
          </div>
        </form>

        {/* Batch Upload Section */}
        {showBatchUpload && onBatchUpload && (
          <div className="border-t pt-6">
            <h3 className="text-lg font-medium mb-4">Batch Property Upload</h3>
            <Alert className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Upload a CSV file with columns: address, city, state, zip_code, parcel_id (optional), search_radius (optional)
              </AlertDescription>
            </Alert>
            
            <div className="space-y-4">
              <Input
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              
              {batchFile && (
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">{batchFile.name}</span>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setBatchFile(null)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
              
              <Button
                onClick={processBatchUpload}
                disabled={!batchFile || batchProcessing}
                className="w-full"
              >
                {batchProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Process CSV File
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
