'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { PropertyInputForm } from '@/components/forms/property-input-form';
import { PropertyCard } from '@/components/ui/property-card';
import { PropertyPreview } from '@/components/ui/property-preview';
import { PropertyMap } from '@/components/maps/property-map';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Search,
  MapPin,
  List,
  Grid3X3,
  Filter,
  Download,
  Save,
  Share,
  Loader2,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';
import { Property } from '@/types';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

interface SearchResults {
  searchLocation: any;
  properties: Property[];
  totalFound: number;
  searchRadius: number;
  filters: any;
}

function PropertySearchContent() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [searchResults, setSearchResults] = useState<SearchResults | null>(null);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'map'>('grid');
  const [comparisonList, setComparisonList] = useState<Property[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
    }
  }, [session, status, router]);

  // Handle property search
  const handlePropertySearch = async (data: any) => {
    setLoading(true);
    try {
      const response = await fetch('/api/properties/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          location: data.address,
          radius: data.searchRadius,
          filters: {
            // Add any additional filters here
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Search failed');
      }

      const result = await response.json();
      if (result.success) {
        setSearchResults(result.data);
        setSelectedProperty(result.data.properties[0] || null);
        toast.success(`Found ${result.data.totalFound} properties`);
      } else {
        throw new Error(result.error || 'Search failed');
      }
    } catch (error) {
      console.error('Search error:', error);
      toast.error('Failed to search properties. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle batch property upload
  const handleBatchUpload = async (properties: any[]) => {
    setLoading(true);
    try {
      // Process each property in the batch
      const batchResults = await Promise.all(
        properties.map(async (property) => {
          const response = await fetch('/api/properties/search', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              location: property.address,
              radius: property.searchRadius || 5,
            }),
          });

          if (response.ok) {
            const result = await response.json();
            return result.data.properties;
          }
          return [];
        })
      );

      // Flatten and combine results
      const allProperties = batchResults.flat();

      setSearchResults({
        searchLocation: { address: 'Batch Upload Results' },
        properties: allProperties,
        totalFound: allProperties.length,
        searchRadius: 5,
        filters: {},
      });

      toast.success(`Processed ${properties.length} addresses, found ${allProperties.length} properties`);
    } catch (error) {
      console.error('Batch upload error:', error);
      toast.error('Failed to process batch upload');
    } finally {
      setLoading(false);
    }
  };

  // Handle property selection
  const handlePropertySelect = (property: Property) => {
    setSelectedProperty(property);
  };

  // Handle property comparison
  const handlePropertyCompare = (property: Property) => {
    const isAlreadyInComparison = comparisonList.some(p => p.id === property.id);

    if (isAlreadyInComparison) {
      setComparisonList(comparisonList.filter(p => p.id !== property.id));
      toast.success('Property removed from comparison');
    } else if (comparisonList.length >= 5) {
      toast.error('Maximum 5 properties can be compared at once');
    } else {
      setComparisonList([...comparisonList, property]);
      toast.success('Property added to comparison');
    }
  };

  // Handle property save
  const handlePropertySave = (property: Property) => {
    // In a real app, this would save to user's saved properties
    toast.success('Property saved to your list');
  };

  // Handle property share
  const handlePropertyShare = (property: Property) => {
    const shareUrl = `${window.location.origin}/properties/${property.id}`;
    navigator.clipboard.writeText(shareUrl);
    toast.success('Property link copied to clipboard');
  };

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Unauthenticated state
  if (!session) {
    return null;
  }



  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Property Search & Analysis
          </h1>
          <p className="text-gray-600">
            Find and analyze properties for development opportunities using AI-powered evaluation.
          </p>
        </div>

        {/* Search Form */}
        <div className="mb-8">
          <PropertyInputForm
            onSubmit={handlePropertySearch}
            onBatchUpload={handleBatchUpload}
            loading={loading}
          />
        </div>

        {/* Search Results */}
        {searchResults && (
          <div className="space-y-6">
            {/* Results Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h2 className="text-xl font-semibold">
                  Search Results ({searchResults.totalFound} properties)
                </h2>
                <Badge variant="outline">
                  {searchResults.searchRadius} mile radius
                </Badge>
              </div>

              <div className="flex items-center gap-2">
                {/* View Mode Toggle */}
                <div className="flex rounded-lg border">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'map' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('map')}
                  >
                    <MapPin className="h-4 w-4" />
                  </Button>
                </div>

                {/* Action Buttons */}
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-1" />
                  Filters
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Export
                </Button>
              </div>
            </div>

            {/* Comparison Bar */}
            {comparisonList.length > 0 && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription className="flex items-center justify-between">
                  <span>
                    {comparisonList.length} properties selected for comparison
                  </span>
                  <Button
                    size="sm"
                    onClick={() => router.push(`/properties/compare?ids=${comparisonList.map(p => p.id).join(',')}`)}
                  >
                    Compare Properties
                  </Button>
                </AlertDescription>
              </Alert>
            )}

            {/* Results Content */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Property List/Grid */}
              <div className={viewMode === 'map' ? 'lg:col-span-2' : 'lg:col-span-2'}>
                {viewMode === 'map' ? (
                  <Card className="h-[600px]">
                    <CardContent className="p-0 h-full">
                      <PropertyMap
                        properties={searchResults.properties}
                        center={searchResults.searchLocation.coordinates}
                        onPropertySelect={handlePropertySelect}
                        selectedProperty={selectedProperty || undefined}
                        height="600px"
                        showTransitStops={true}
                        showZoning={true}
                      />
                    </CardContent>
                  </Card>
                ) : (
                  <div className={cn(
                    'space-y-4',
                    viewMode === 'grid' && 'grid grid-cols-1 md:grid-cols-2 gap-4 space-y-0'
                  )}>
                    {searchResults.properties.map((property) => (
                      <PropertyCard
                        key={property.id}
                        property={property}
                        showEvaluation={true}
                        showActions={true}
                        onCompare={handlePropertyCompare}
                        onSave={handlePropertySave}
                        onShare={handlePropertyShare}
                        isSelected={selectedProperty?.id === property.id}
                        isComparing={comparisonList.some(p => p.id === property.id)}
                        className="cursor-pointer"
                        onClick={() => handlePropertySelect(property)}
                      />
                    ))}
                  </div>
                )}
              </div>

              {/* Property Preview */}
              <div className="lg:col-span-1">
                <div className="sticky top-6">
                  <PropertyPreview
                    property={selectedProperty}
                    loading={loading}
                    onSave={handlePropertySave}
                    onShare={handlePropertyShare}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!searchResults && !loading && (
          <Card className="text-center py-12">
            <CardContent>
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Start Your Property Search
              </h3>
              <p className="text-gray-500 max-w-md mx-auto">
                Enter a property address above to begin searching for development opportunities
                in the area. Our AI will analyze each property against 200+ criteria.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

export default function PropertySearchPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent hydration issues by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return <PropertySearchContent />;
}
