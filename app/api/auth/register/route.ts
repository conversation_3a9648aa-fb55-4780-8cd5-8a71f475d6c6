import { NextRequest, NextResponse } from 'next/server';
// import { connectToDatabase, UserModel } from '@/lib/db/models';

export async function POST(request: NextRequest) {
  try {
    const { name, email, password } = await request.json();

    // Validate input
    if (!name || !email || !password) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (password.length < 8) {
      return NextResponse.json(
        { message: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // For demo purposes, just return success
    // In production, you would save to database
    console.log('Demo registration:', { name, email });

    return NextResponse.json(
      {
        message: 'User created successfully (demo mode)',
        user: {
          id: Date.now().toString(),
          name,
          email,
          role: 'user',
        }
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
