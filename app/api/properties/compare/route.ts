import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { PropertyComparisonEngine } from '@/lib/comparison/property-comparison';
import { PropertyEvaluationEngine } from '@/lib/evaluation/criteria-engine';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { propertyIds, customCriteria } = body;

    if (!propertyIds || !Array.isArray(propertyIds) || propertyIds.length < 2) {
      return NextResponse.json(
        { error: 'At least 2 property IDs are required for comparison' },
        { status: 400 }
      );
    }

    if (propertyIds.length > 5) {
      return NextResponse.json(
        { error: 'Maximum 5 properties can be compared at once' },
        { status: 400 }
      );
    }

    // Mock properties for demo
    const properties = propertyIds.map((id: string, index: number) => ({
      id,
      address: `${123 + index * 100} Demo Street ${index + 1}`,
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210',
      parcelId: `APN-${id}`,
      coordinates: {
        lat: 34.0522 + (index * 0.01),
        lng: -118.2437 + (index * 0.01)
      },
      lotSize: 20000 + (index * 2500),
      dimensions: {
        width: 140 + (index * 10),
        length: 140 + (index * 15)
      },
      zoning: ['R3', 'R4', 'R3', 'R4', 'R5'][index] || 'R3',
      assessedValue: 800000 + (index * 150000),
      taxAmount: 9600 + (index * 1800),
      owner: {
        name: `Demo Owner ${index + 1}`,
        address: `${456 + index * 100} Owner St, Los Angeles, CA 90210`,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    // Get evaluations for each property
    const evaluations = await Promise.all(
      properties.map(async (property) => {
        try {
          return await PropertyEvaluationEngine.evaluateProperty(property);
        } catch (error) {
          console.error(`Failed to evaluate property ${property.id}:`, error);
          return null;
        }
      })
    );

    // Perform comparison
    const comparison = PropertyComparisonEngine.compareProperties(
      properties,
      evaluations,
      customCriteria,
      session.user?.email || 'anonymous'
    );

    return NextResponse.json({
      success: true,
      data: comparison,
    });

  } catch (error) {
    console.error('Property comparison error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const comparisonId = searchParams.get('comparisonId');

    if (comparisonId) {
      // Get specific comparison (mock for demo)
      const mockComparison = {
        id: comparisonId,
        properties: [
          {
            id: 'prop-1',
            address: '123 Demo Street 1, Los Angeles, CA 90210',
            assessedValue: 800000,
            lotSize: 20000,
            zoning: 'R3'
          },
          {
            id: 'prop-2',
            address: '223 Demo Street 2, Los Angeles, CA 90210',
            assessedValue: 950000,
            lotSize: 22500,
            zoning: 'R4'
          }
        ],
        summary: {
          overallWinner: 1,
          recommendations: [
            'Property 2 offers better development potential',
            'Consider the higher acquisition cost vs. potential returns'
          ]
        },
        createdAt: new Date(),
        createdBy: session.user?.email
      };

      return NextResponse.json({
        success: true,
        data: mockComparison,
      });
    }

    // Get user's comparison history (mock for demo)
    const mockComparisons = [
      {
        id: 'comp-1',
        name: 'Downtown Properties Comparison',
        propertyCount: 3,
        createdAt: new Date(Date.now() - 86400000), // 1 day ago
        overallWinner: 'Property 2'
      },
      {
        id: 'comp-2',
        name: 'Hollywood vs Santa Monica',
        propertyCount: 2,
        createdAt: new Date(Date.now() - 172800000), // 2 days ago
        overallWinner: 'Property 1'
      }
    ];

    return NextResponse.json({
      success: true,
      data: mockComparisons,
    });

  } catch (error) {
    console.error('Get comparisons error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
