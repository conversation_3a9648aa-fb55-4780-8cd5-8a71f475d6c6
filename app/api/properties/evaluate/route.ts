import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { PropertyEvaluationEngine } from '@/lib/evaluation/criteria-engine';
// import { connectToDatabase, PropertyModel } from '@/lib/db/models';
import { Property } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { propertyId, propertyData } = body;

    if (!propertyId && !propertyData) {
      return NextResponse.json(
        { error: 'Property ID or property data is required' },
        { status: 400 }
      );
    }

    let property: Property;

    if (propertyId) {
      // For demo, create a mock property
      property = {
        id: propertyId,
        address: '123 Demo Street',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        parcelId: 'APN-*********',
        coordinates: { lat: 34.0522, lng: -118.2437 },
        lotSize: 22500,
        dimensions: { width: 150, length: 150 },
        zoning: 'R3',
        assessedValue: 850000,
        taxAmount: 10200,
        owner: {
          name: 'Demo Owner',
          address: '456 Owner St, Los Angeles, CA 90210',
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    } else {
      // Use provided property data
      property = propertyData;
    }

    // Evaluate the property
    const evaluationResults = await PropertyEvaluationEngine.evaluateProperty(property);

    // In demo mode, we skip saving to database
    console.log('Demo: Property evaluated', { propertyId, score: evaluationResults.overallScore });

    return NextResponse.json({
      success: true,
      data: {
        property,
        evaluation: evaluationResults,
      },
    });

  } catch (error) {
    console.error('Property evaluation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const propertyId = searchParams.get('propertyId');

    if (!propertyId) {
      return NextResponse.json(
        { error: 'Property ID is required' },
        { status: 400 }
      );
    }

    // For demo, create a mock property
    const property = {
      id: propertyId,
      address: '123 Demo Street',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210',
      parcelId: 'APN-*********',
      coordinates: { lat: 34.0522, lng: -118.2437 },
      lotSize: 22500,
      dimensions: { width: 150, length: 150 },
      zoning: 'R3',
      assessedValue: 850000,
      taxAmount: 10200,
      owner: {
        name: 'Demo Owner',
        address: '456 Owner St, Los Angeles, CA 90210',
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      evaluationResults: null, // Will be populated if evaluated
    };

    return NextResponse.json({
      success: true,
      data: {
        property,
        hasEvaluation: !!property.evaluationResults,
      },
    });

  } catch (error) {
    console.error('Get property evaluation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
