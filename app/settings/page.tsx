'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';


import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  User, 
  Bell, 
  Shield, 
  Database,
  Key,
  Mail,
  Save,
  AlertTriangle
} from 'lucide-react';
import toast from 'react-hot-toast';

function SettingsContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  const [activeTab, setActiveTab] = useState('profile');
  const [loading, setLoading] = useState(false);
  
  // Profile settings
  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
  });

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    propertyAlerts: true,
    reportGeneration: true,
    marketUpdates: false,
    weeklyDigest: true,
  });

  // API settings
  const [apiSettings, setApiSettings] = useState({
    googleMapsApiKey: '',
    openaiApiKey: '',
    webhookUrl: '',
  });

  // Evaluation criteria settings
  const [criteriaSettings, setCriteriaSettings] = useState({
    minLotSize: 20000,
    maxLotSize: 40000,
    minUnits: 250,
    minHeight: 65,
    requireQctDda: true,
    maxTransitDistance: 0.25,
    maxPeakFrequency: 15,
    maxOffPeakFrequency: 30,
  });

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    // Load user settings
    loadUserSettings();
  }, [session, status, router]);

  const loadUserSettings = async () => {
    try {
      // In a real app, this would fetch from your API
      setProfileData({
        name: session?.user?.name || '',
        email: session?.user?.email || '',
        company: '',
        phone: '',
      });
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const handleSaveProfile = async () => {
    setLoading(true);
    try {
      // In a real app, this would save to your API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Profile updated successfully');
    } catch (error) {
      toast.error('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveNotifications = async () => {
    setLoading(true);
    try {
      // In a real app, this would save to your API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Notification settings updated');
    } catch (error) {
      toast.error('Failed to update notification settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveApiSettings = async () => {
    setLoading(true);
    try {
      // In a real app, this would save to your API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('API settings updated');
    } catch (error) {
      toast.error('Failed to update API settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveCriteria = async () => {
    setLoading(true);
    try {
      // In a real app, this would save to your API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Evaluation criteria updated');
    } catch (error) {
      toast.error('Failed to update evaluation criteria');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'api', label: 'API Keys', icon: Key },
    { id: 'criteria', label: 'Evaluation Criteria', icon: Database },
    { id: 'security', label: 'Security', icon: Shield },
  ];

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) return null;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage your account settings and preferences
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-800'
                    }`}
                  >
                    <Icon className="w-5 h-5 mr-3" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1">
            {/* Profile Tab */}
            {activeTab === 'profile' && (
              <Card>
                <CardHeader>
                  <CardTitle>Profile Information</CardTitle>
                  <CardDescription>
                    Update your personal information and contact details
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Full Name
                      </label>
                      <Input
                        type="text"
                        value={profileData.name}
                        onChange={(e) => setProfileData({...profileData, name: e.target.value})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email Address
                      </label>
                      <Input
                        type="email"
                        value={profileData.email}
                        onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Company
                      </label>
                      <Input
                        type="text"
                        value={profileData.company}
                        onChange={(e) => setProfileData({...profileData, company: e.target.value})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Phone Number
                      </label>
                      <Input
                        type="tel"
                        value={profileData.phone}
                        onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                      />
                    </div>
                  </div>
                  <Button onClick={handleSaveProfile} disabled={loading}>
                    <Save className="w-4 h-4 mr-2" />
                    Save Changes
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Notifications Tab */}
            {activeTab === 'notifications' && (
              <Card>
                <CardHeader>
                  <CardTitle>Notification Preferences</CardTitle>
                  <CardDescription>
                    Choose how you want to be notified about important updates
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    {Object.entries(notificationSettings).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {getNotificationDescription(key)}
                          </p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => setNotificationSettings({
                              ...notificationSettings,
                              [key]: e.target.checked
                            })}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    ))}
                  </div>
                  <Button onClick={handleSaveNotifications} disabled={loading}>
                    <Save className="w-4 h-4 mr-2" />
                    Save Preferences
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* API Keys Tab */}
            {activeTab === 'api' && (
              <Card>
                <CardHeader>
                  <CardTitle>API Configuration</CardTitle>
                  <CardDescription>
                    Configure API keys for external services
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <div className="flex items-start">
                      <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-3" />
                      <div>
                        <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                          Security Notice
                        </h4>
                        <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                          API keys are encrypted and stored securely. Never share your API keys with others.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Google Maps API Key
                      </label>
                      <Input
                        type="password"
                        placeholder="Enter your Google Maps API key"
                        value={apiSettings.googleMapsApiKey}
                        onChange={(e) => setApiSettings({...apiSettings, googleMapsApiKey: e.target.value})}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Required for mapping and geocoding features
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        OpenAI API Key
                      </label>
                      <Input
                        type="password"
                        placeholder="Enter your OpenAI API key"
                        value={apiSettings.openaiApiKey}
                        onChange={(e) => setApiSettings({...apiSettings, openaiApiKey: e.target.value})}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Required for AI-powered document analysis and recommendations
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Webhook URL
                      </label>
                      <Input
                        type="url"
                        placeholder="https://your-domain.com/webhook"
                        value={apiSettings.webhookUrl}
                        onChange={(e) => setApiSettings({...apiSettings, webhookUrl: e.target.value})}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Optional: Receive notifications when reports are generated
                      </p>
                    </div>
                  </div>

                  <Button onClick={handleSaveApiSettings} disabled={loading}>
                    <Save className="w-4 h-4 mr-2" />
                    Save API Settings
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Evaluation Criteria Tab */}
            {activeTab === 'criteria' && (
              <Card>
                <CardHeader>
                  <CardTitle>Evaluation Criteria</CardTitle>
                  <CardDescription>
                    Customize the criteria used for property evaluation
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Minimum Lot Size (sq ft)
                      </label>
                      <Input
                        type="number"
                        value={criteriaSettings.minLotSize}
                        onChange={(e) => setCriteriaSettings({...criteriaSettings, minLotSize: parseInt(e.target.value)})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Maximum Lot Size (sq ft)
                      </label>
                      <Input
                        type="number"
                        value={criteriaSettings.maxLotSize}
                        onChange={(e) => setCriteriaSettings({...criteriaSettings, maxLotSize: parseInt(e.target.value)})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Minimum Units Required
                      </label>
                      <Input
                        type="number"
                        value={criteriaSettings.minUnits}
                        onChange={(e) => setCriteriaSettings({...criteriaSettings, minUnits: parseInt(e.target.value)})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Minimum Height (ft)
                      </label>
                      <Input
                        type="number"
                        value={criteriaSettings.minHeight}
                        onChange={(e) => setCriteriaSettings({...criteriaSettings, minHeight: parseInt(e.target.value)})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Max Transit Distance (miles)
                      </label>
                      <Input
                        type="number"
                        step="0.1"
                        value={criteriaSettings.maxTransitDistance}
                        onChange={(e) => setCriteriaSettings({...criteriaSettings, maxTransitDistance: parseFloat(e.target.value)})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Max Peak Frequency (min)
                      </label>
                      <Input
                        type="number"
                        value={criteriaSettings.maxPeakFrequency}
                        onChange={(e) => setCriteriaSettings({...criteriaSettings, maxPeakFrequency: parseInt(e.target.value)})}
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                        Require QCT/DDA Status
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Only consider properties in Qualified Census Tracts or Difficult Development Areas
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={criteriaSettings.requireQctDda}
                        onChange={(e) => setCriteriaSettings({...criteriaSettings, requireQctDda: e.target.checked})}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <Button onClick={handleSaveCriteria} disabled={loading}>
                    <Save className="w-4 h-4 mr-2" />
                    Save Criteria
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <Card>
                <CardHeader>
                  <CardTitle>Security Settings</CardTitle>
                  <CardDescription>
                    Manage your account security and privacy settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <Button variant="outline" className="w-full justify-start">
                      <Key className="w-4 h-4 mr-2" />
                      Change Password
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Shield className="w-4 h-4 mr-2" />
                      Enable Two-Factor Authentication
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Mail className="w-4 h-4 mr-2" />
                      Download Account Data
                    </Button>
                  </div>

                  <div className="border-t pt-6">
                    <h4 className="text-sm font-medium text-red-600 dark:text-red-400 mb-2">
                      Danger Zone
                    </h4>
                    <Button variant="outline" className="text-red-600 border-red-600 hover:bg-red-50">
                      Delete Account
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function SettingsPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent hydration issues by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return <SettingsContent />;
}

function getNotificationDescription(key: string): string {
  const descriptions: { [key: string]: string } = {
    emailNotifications: 'Receive email notifications for important updates',
    propertyAlerts: 'Get notified when new properties match your criteria',
    reportGeneration: 'Receive notifications when reports are generated',
    marketUpdates: 'Stay informed about market trends and changes',
    weeklyDigest: 'Get a weekly summary of your activity',
  };
  return descriptions[key] || '';
}
