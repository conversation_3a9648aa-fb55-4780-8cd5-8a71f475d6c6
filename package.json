{"name": "aquisitions-automation", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "type-check:detailed": "node scripts/check-types.js", "test": "jest", "test:watch": "jest --watch", "prebuild": "npm run type-check"}, "dependencies": {"@auth/mongodb-adapter": "^3.7.4", "@hookform/resolvers": "^3.9.1", "@tanstack/react-query": "^5.62.7", "@types/puppeteer": "^5.4.7", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.15.0", "lucide-react": "^0.468.0", "mongodb": "^6.12.0", "mongoose": "^8.8.4", "next": "^15.3.3", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "openai": "^4.77.3", "puppeteer": "^23.10.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/google.maps": "^3.58.1", "@types/jest": "^29.5.14", "@types/node": "^24.0.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "^15.3.3", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "postcss": "^8.4.35", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}