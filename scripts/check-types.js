#!/usr/bin/env node

/**
 * TypeScript Error Checking Script
 * Runs TypeScript compilation and provides detailed error reporting
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Running TypeScript type checking...\n');

try {
  // Run TypeScript compilation
  const output = execSync('npx tsc --noEmit --pretty', {
    encoding: 'utf8',
    stdio: 'pipe'
  });

  console.log('✅ No TypeScript errors found!');
  if (output.trim()) {
    console.log(output);
  }

} catch (error) {
  console.log('❌ TypeScript errors found:\n');

  // Get both stdout and stderr
  const errorOutput = error.stdout || error.stderr || error.message;
  console.log(errorOutput);

  // Parse and categorize errors
  const errorLines = errorOutput.split('\n').filter(line => line.includes('error TS'));
  const errorsByType = {};

  errorLines.forEach(line => {
    const match = line.match(/error (TS\d+):/);
    if (match) {
      const errorCode = match[1];
      if (!errorsByType[errorCode]) {
        errorsByType[errorCode] = [];
      }
      errorsByType[errorCode].push(line);
    }
  });

  console.log('\n📊 Error Summary:');
  Object.entries(errorsByType).forEach(([code, errors]) => {
    console.log(`  ${code}: ${errors.length} errors`);
  });

  console.log(`\n📈 Total errors: ${errorLines.length}`);

  // Provide suggestions for common errors
  console.log('\n💡 Common fixes:');
  if (errorsByType['TS2532']) {
    console.log('  - TS2532: Add null checks or use optional chaining (?.)');
  }
  if (errorsByType['TS2345']) {
    console.log('  - TS2345: Check argument types and add proper type assertions');
  }
  if (errorsByType['TS2322']) {
    console.log('  - TS2322: Ensure types match exactly, consider using type guards');
  }
  if (errorsByType['TS2375']) {
    console.log('  - TS2375: With exactOptionalPropertyTypes, avoid undefined for optional props');
  }
  if (errorsByType['TS2379']) {
    console.log('  - TS2379: exactOptionalPropertyTypes - remove undefined from optional properties');
  }
  if (errorsByType['TS18048']) {
    console.log('  - TS18048: Add null/undefined checks before accessing properties');
  }

  process.exit(1);
}
