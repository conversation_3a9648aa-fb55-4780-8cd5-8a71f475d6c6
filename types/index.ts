// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user';
  createdAt: Date;
  updatedAt: Date;
}

// Property Types
export interface Property {
  id: string;
  address: string;
  parcelId?: string;
  city: string;
  state: string;
  zipCode: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  lotSize: number; // in square feet
  dimensions: {
    width: number;
    length: number;
  };
  zoning: string;
  assessedValue: number;
  marketValue?: number;
  taxAmount: number;
  yearBuilt?: number;
  propertyType?: string;
  imageUrl?: string;
  owner: PropertyOwner;
  evaluationResults?: EvaluationResults;
  createdAt: Date;
  updatedAt: Date;
}

export interface PropertyOwner {
  name: string;
  address: string;
  phone?: string;
  email?: string;
}

// Evaluation Types
export interface EvaluationCriteria {
  qctDdaStatus: boolean;
  neighborhoodChangeZone: boolean;
  lotSizeAnalysis: LotSizeAnalysis;
  densityPotential: DensityAnalysis;
  heightRestrictions: HeightAnalysis;
  siteLayout: SiteLayoutAnalysis;
  topography: TopographyAnalysis;
  historicalStatus: HistoricalAnalysis;
  environmentalConcerns: EnvironmentalAnalysis;
  transitAccess: TransitAnalysis;
}

export interface LotSizeAnalysis {
  size: number;
  isIdeal: boolean; // 20,000-25,000 sq ft
  isAcceptable: boolean; // up to 40,000 sq ft
  subdivisionPotential: boolean;
  minimumWidth: number;
  meetsWidthRequirement: boolean; // > 75 ft
}

export interface DensityAnalysis {
  baseUnits: number;
  bonusUnits: number;
  totalUnits: number;
  meets250Requirement: boolean;
}

export interface HeightAnalysis {
  baseHeight: number;
  bonusHeight: number;
  totalHeight: number;
  meets65FtRequirement: boolean;
  meetsRequirement: boolean; // alias for meets65FtRequirement
}

export interface SiteLayoutAnalysis {
  frontages: number;
  meetsMinimumFrontages: boolean; // >= 2
  hasPreferredFrontages: boolean; // 3-4
}

export interface TopographyAnalysis {
  gradeChange: number;
  meetsRequirement: boolean; // <= 10 ft
}

export interface HistoricalAnalysis {
  hasDesignation: boolean;
  inHistoricalDistrict: boolean;
  isEligible: boolean; // !hasDesignation && !inHistoricalDistrict
}

export interface EnvironmentalAnalysis {
  hasIssues: boolean;
  requiresRemediation: boolean;
  isEligible: boolean; // !hasIssues
}

export interface TransitAnalysis {
  nearestStopDistance: number; // in miles
  withinQuarterMile: boolean;
  peakFrequency: number; // minutes
  offPeakFrequency: number; // minutes
  meetsPeakRequirement: boolean; // <= 15 minutes
  meetsOffPeakRequirement: boolean; // <= 30 minutes
  meetsRequirements: boolean; // overall transit requirements
  isEligible: boolean;
}

export interface EvaluationResults {
  overallScore: number;
  passed: boolean;
  criteria: EvaluationCriteria;
  recommendations: string[];
  alternativeSuggestions: string[];
  confidenceScore: number;
  evaluatedAt: Date;
}

// Search and Saved Search Types
export interface SavedSearch {
  id: string;
  userId: string;
  name: string;
  criteria: SearchCriteria;
  results: Property[];
  createdAt: Date;
  updatedAt: Date;
}

export interface SearchCriteria {
  location?: string;
  radius?: number; // in miles
  minLotSize?: number;
  maxLotSize?: number;
  minPrice?: number;
  maxPrice?: number;
  zoning?: string[];
  requiresQctDda?: boolean;
}

// Mapping Types
export interface MapMarker {
  id: string;
  position: {
    lat: number;
    lng: number;
  };
  property: Property;
  type: 'property' | 'transit' | 'poi';
}

// Report Types
export interface PropertyReport {
  id: string;
  propertyId: string;
  type: 'summary' | 'detailed' | 'comparison';
  sections: ReportSection[];
  generatedAt: Date;
  generatedBy: string;
}

export interface ReportSection {
  title: string;
  content: string;
  charts?: ChartData[];
  images?: string[];
}

export interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'scatter';
  data: any[];
  labels: string[];
  title: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface PropertySearchForm {
  address: string;
  parcelId?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
}
