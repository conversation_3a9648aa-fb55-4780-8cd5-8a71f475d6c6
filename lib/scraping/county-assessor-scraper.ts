import { BaseScraper, ScrapedData } from './base-scraper';
import { Page } from 'puppeteer';

export interface PropertyAssessmentData {
  parcelId: string;
  address: string;
  owner: {
    name: string;
    address: string;
  };
  assessedValue: number;
  marketValue?: number;
  taxAmount: number;
  lotSize: number;
  yearBuilt?: number;
  buildingArea?: number;
  zoning: string;
  landUse: string;
  saleHistory: Array<{
    date: string;
    price: number;
    buyer?: string;
    seller?: string;
  }>;
  taxHistory: Array<{
    year: number;
    assessedValue: number;
    taxAmount: number;
  }>;
}

export class CountyAssessorScraper extends BaseScraper {
  private countyConfigs: { [key: string]: CountyConfig } = {
    'los-angeles': {
      baseUrl: 'https://portal.assessor.lacounty.gov',
      searchPath: '/parcel/search',
      selectors: {
        searchInput: '#parcel-search',
        searchButton: '.search-btn',
        propertyInfo: '.property-details',
        ownerName: '.owner-name',
        assessedValue: '.assessed-value',
        // ... more selectors
      }
    },
    'orange': {
      baseUrl: 'https://ac.ocgov.com',
      searchPath: '/search',
      selectors: {
        searchInput: '',      // TODO: Replace with actual selector
        searchButton: '',     // TODO: Replace with actual selector
        propertyInfo: '',     // TODO: Replace with actual selector
        ownerName: '',        // TODO: Replace with actual selector
        assessedValue: '',    // TODO: Replace with actual selector
      }
    },
    // Add more counties as needed
  };

  async scrape(parcelId: string, county: string = 'los-angeles'): Promise<ScrapedData> {
    const config = this.countyConfigs[county];
    if (!config) {
      throw new Error(`County configuration not found: ${county}`);
    }

    let page: Page | null = null;
    
    try {
      page = await this.createPage();
      const url = `${config.baseUrl}${config.searchPath}`;
      
      await this.navigateWithRetry(page, url);
      
      // Handle potential bot protection
      if (await this.checkForBotProtection(page)) {
        await this.handleCaptcha(page);
      }

      const data = await this.scrapePropertyData(page, parcelId, config);
      
      return {
        source: 'county-assessor',
        url,
        data,
        scrapedAt: new Date(),
        success: true,
      };
    } catch (error) {
      await this.handleError(error as Error, 'county-assessor-scraper');
      
      return {
        source: 'county-assessor',
        url: config.baseUrl,
        data: null,
        scrapedAt: new Date(),
        success: false,
        error: (error as Error).message,
      };
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  private async scrapePropertyData(
    page: Page, 
    parcelId: string, 
    config: CountyConfig
  ): Promise<PropertyAssessmentData> {
    // Search for the parcel
    await this.waitForElementWithRetry(page, config.selectors.searchInput);
    await page.type(config.selectors.searchInput, parcelId);
    await page.click(config.selectors.searchButton);
    
    // Wait for results
    await this.waitForElementWithRetry(page, config.selectors.propertyInfo);
    
    // Extract property information
    const address = await this.extractText(page, '.property-address') || '';
    const ownerName = await this.extractText(page, config.selectors.ownerName) || '';
    const ownerAddress = await this.extractText(page, '.owner-address') || '';
    
    // Extract financial data
    const assessedValueText = await this.extractText(page, config.selectors.assessedValue) || '0';
    const assessedValue = this.parseNumber(assessedValueText);
    
    const marketValueText = await this.extractText(page, '.market-value') || '0';
    const marketValue = this.parseNumber(marketValueText);
    
    const taxAmountText = await this.extractText(page, '.tax-amount') || '0';
    const taxAmount = this.parseNumber(taxAmountText);
    
    // Extract property characteristics
    const lotSizeText = await this.extractText(page, '.lot-size') || '0';
    const lotSize = this.parseNumber(lotSizeText);
    
    const zoning = await this.extractText(page, '.zoning') || '';
    const landUse = await this.extractText(page, '.land-use') || '';
    
    const yearBuiltText = await this.extractText(page, '.year-built');
    const yearBuilt = yearBuiltText ? parseInt(yearBuiltText) : undefined;
    
    const buildingAreaText = await this.extractText(page, '.building-area');
    const buildingArea = buildingAreaText ? this.parseNumber(buildingAreaText) : undefined;
    
    // Extract sale history
    const saleHistory = await this.extractSaleHistory(page);
    
    // Extract tax history
    const taxHistory = await this.extractTaxHistory(page);
    
    return {
      parcelId,
      address,
      owner: {
        name: ownerName,
        address: ownerAddress,
      },
      assessedValue,
      marketValue,
      taxAmount,
      lotSize,
      ...(yearBuilt !== undefined && { yearBuilt }),
      ...(buildingArea !== undefined && { buildingArea }),
      zoning,
      landUse,
      saleHistory,
      taxHistory,
    };
  }

  private async extractSaleHistory(page: Page): Promise<Array<{
    date: string;
    price: number;
    buyer?: string;
    seller?: string;
  }>> {
    const saleHistory: Array<{
      date: string;
      price: number;
      buyer?: string;
      seller?: string;
    }> = [];

    try {
      // Check if sale history section exists
      const saleHistorySection = await page.$('.sale-history');
      if (!saleHistorySection) return saleHistory;

      // Extract sale records
      const saleRows = await page.$$('.sale-history .sale-row');
      
      for (const row of saleRows) {
        const date = await page.evaluate(el => 
          el.querySelector('.sale-date')?.textContent?.trim(), row) || '';
        
        const priceText = await page.evaluate(el => 
          el.querySelector('.sale-price')?.textContent?.trim(), row) || '0';
        const price = this.parseNumber(priceText);
        
        const buyer = await page.evaluate(el => 
          el.querySelector('.buyer')?.textContent?.trim(), row);
        
        const seller = await page.evaluate(el => 
          el.querySelector('.seller')?.textContent?.trim(), row);
        
        if (date && price > 0) {
          saleHistory.push({
            date,
            price,
            ...(buyer && { buyer }),
            ...(seller && { seller }),
          });
        }
      }
    } catch (error) {
      console.warn('Failed to extract sale history:', error);
    }

    return saleHistory;
  }

  private async extractTaxHistory(page: Page): Promise<Array<{
    year: number;
    assessedValue: number;
    taxAmount: number;
  }>> {
    const taxHistory: Array<{
      year: number;
      assessedValue: number;
      taxAmount: number;
    }> = [];

    try {
      // Check if tax history section exists
      const taxHistorySection = await page.$('.tax-history');
      if (!taxHistorySection) return taxHistory;

      // Extract tax records
      const taxRows = await page.$$('.tax-history .tax-row');
      
      for (const row of taxRows) {
        const yearText = await page.evaluate(el => 
          el.querySelector('.tax-year')?.textContent?.trim(), row) || '';
        const year = parseInt(yearText);
        
        const assessedValueText = await page.evaluate(el => 
          el.querySelector('.assessed-value')?.textContent?.trim(), row) || '0';
        const assessedValue = this.parseNumber(assessedValueText);
        
        const taxAmountText = await page.evaluate(el => 
          el.querySelector('.tax-amount')?.textContent?.trim(), row) || '0';
        const taxAmount = this.parseNumber(taxAmountText);
        
        if (year && assessedValue > 0) {
          taxHistory.push({
            year,
            assessedValue,
            taxAmount,
          });
        }
      }
    } catch (error) {
      console.warn('Failed to extract tax history:', error);
    }

    return taxHistory;
  }

  private parseNumber(text: string): number {
    // Remove currency symbols, commas, and other non-numeric characters
    const cleaned = text.replace(/[$,\s]/g, '');
    const number = parseFloat(cleaned);
    return isNaN(number) ? 0 : number;
  }

  /**
   * Detect county based on coordinates or address
   */
  static detectCounty(coordinates?: { lat: number; lng: number }, address?: string): string {
    if (coordinates) {
      // Los Angeles County approximate bounds
      if (coordinates.lat >= 33.7 && coordinates.lat <= 34.8 && 
          coordinates.lng >= -118.9 && coordinates.lng <= -117.6) {
        return 'los-angeles';
      }
      
      // Orange County approximate bounds
      if (coordinates.lat >= 33.4 && coordinates.lat <= 33.9 && 
          coordinates.lng >= -118.1 && coordinates.lng <= -117.4) {
        return 'orange';
      }
    }

    if (address) {
      const addressLower = address.toLowerCase();
      if (addressLower.includes('los angeles') || addressLower.includes(', ca')) {
        return 'los-angeles';
      }
      if (addressLower.includes('orange') || addressLower.includes('anaheim') || 
          addressLower.includes('irvine')) {
        return 'orange';
      }
    }

    // Default to Los Angeles County
    return 'los-angeles';
  }
}

interface CountyConfig {
  baseUrl: string;
  searchPath: string;
  selectors: {
    searchInput: string;
    searchButton: string;
    propertyInfo: string;
    ownerName: string;
    assessedValue: string;
    [key: string]: string;
  };
}
