import { PropertyReport } from '@/lib/reports/report-generator';

export interface ShareSettings {
  id: string;
  reportId: string;
  shareType: 'public' | 'private' | 'password' | 'expiring';
  permissions: SharePermissions;
  recipients?: ShareRecipient[];
  expiresAt?: Date;
  password?: string;
  allowDownload: boolean;
  allowComments: boolean;
  trackViews: boolean;
  createdBy: string;
  createdAt: Date;
}

export interface SharePermissions {
  canView: boolean;
  canComment: boolean;
  canDownload: boolean;
  canShare: boolean;
  canEdit: boolean;
}

export interface ShareRecipient {
  email: string;
  name?: string;
  permissions: SharePermissions;
  notified: boolean;
  lastViewed?: Date;
}

export interface ShareLink {
  id: string;
  url: string;
  token: string;
  reportId: string;
  settings: ShareSettings;
  analytics: ShareAnalytics;
}

export interface ShareAnalytics {
  totalViews: number;
  uniqueViewers: number;
  lastViewed: Date;
  viewHistory: ViewRecord[];
  downloadCount: number;
  commentCount: number;
}

export interface ViewRecord {
  viewerId?: string;
  viewerEmail?: string;
  viewedAt: Date;
  ipAddress: string;
  userAgent: string;
  duration?: number;
}

export interface ReportComment {
  id: string;
  reportId: string;
  shareId: string;
  authorEmail: string;
  authorName?: string;
  content: string;
  sectionId?: string;
  position?: { x: number; y: number };
  createdAt: Date;
  updatedAt?: Date;
  replies?: ReportComment[];
  resolved: boolean;
}

export class ReportSharingService {
  private static shareLinks: Map<string, ShareLink> = new Map();
  private static comments: Map<string, ReportComment[]> = new Map();

  /**
   * Create a shareable link for a report
   */
  static createShareLink(
    reportId: string,
    settings: Omit<ShareSettings, 'id' | 'reportId' | 'createdAt'>,
    userId: string
  ): ShareLink {
    const shareId = this.generateShareId();
    const token = this.generateSecureToken();
    
    const shareSettings: ShareSettings = {
      id: shareId,
      reportId,
      ...settings,
      createdBy: userId,
      createdAt: new Date()
    };

    const shareLink: ShareLink = {
      id: shareId,
      url: `${process.env.NEXT_PUBLIC_APP_URL}/shared/reports/${shareId}?token=${token}`,
      token,
      reportId,
      settings: shareSettings,
      analytics: {
        totalViews: 0,
        uniqueViewers: 0,
        lastViewed: new Date(),
        viewHistory: [],
        downloadCount: 0,
        commentCount: 0
      }
    };

    this.shareLinks.set(shareId, shareLink);

    // Send notifications to recipients if specified
    if (settings.recipients) {
      this.notifyRecipients(shareLink, settings.recipients);
    }

    return shareLink;
  }

  /**
   * Get share link by ID
   */
  static getShareLink(shareId: string): ShareLink | null {
    return this.shareLinks.get(shareId) || null;
  }

  /**
   * Validate access to a shared report
   */
  static validateAccess(
    shareId: string,
    token: string,
    password?: string,
    userEmail?: string
  ): { valid: boolean; permissions?: SharePermissions; error?: string } {
    const shareLink = this.shareLinks.get(shareId);
    
    if (!shareLink) {
      return { valid: false, error: 'Share link not found' };
    }

    // Check if link has expired
    if (shareLink.settings.expiresAt && shareLink.settings.expiresAt < new Date()) {
      return { valid: false, error: 'Share link has expired' };
    }

    // Validate token
    if (shareLink.token !== token) {
      return { valid: false, error: 'Invalid access token' };
    }

    // Check password if required
    if (shareLink.settings.shareType === 'password') {
      if (!password || shareLink.settings.password !== password) {
        return { valid: false, error: 'Password required or incorrect' };
      }
    }

    // Check recipient permissions for private shares
    if (shareLink.settings.shareType === 'private' && shareLink.settings.recipients) {
      const recipient = shareLink.settings.recipients.find(r => r.email === userEmail);
      if (!recipient) {
        return { valid: false, error: 'Access denied - not in recipient list' };
      }
      return { valid: true, permissions: recipient.permissions };
    }

    // Return default permissions for public/password shares
    return { 
      valid: true, 
      permissions: shareLink.settings.permissions 
    };
  }

  /**
   * Track view of a shared report
   */
  static trackView(
    shareId: string,
    viewerInfo: {
      email?: string;
      ipAddress: string;
      userAgent: string;
    }
  ): void {
    const shareLink = this.shareLinks.get(shareId);
    if (!shareLink) return;

    const viewRecord: ViewRecord = {
      viewedAt: new Date(),
      ipAddress: viewerInfo.ipAddress,
      userAgent: viewerInfo.userAgent,
      ...(viewerInfo.email && { viewerEmail: viewerInfo.email }),
    };

    shareLink.analytics.viewHistory.push(viewRecord);
    shareLink.analytics.totalViews++;
    shareLink.analytics.lastViewed = new Date();

    // Count unique viewers
    const uniqueEmails = new Set(
      shareLink.analytics.viewHistory
        .map(v => v.viewerEmail)
        .filter(Boolean)
    );
    shareLink.analytics.uniqueViewers = uniqueEmails.size;

    // Update recipient last viewed time
    if (viewerInfo.email && shareLink.settings.recipients) {
      const recipient = shareLink.settings.recipients.find(r => r.email === viewerInfo.email);
      if (recipient) {
        recipient.lastViewed = new Date();
      }
    }
  }

  /**
   * Track download of a shared report
   */
  static trackDownload(shareId: string): void {
    const shareLink = this.shareLinks.get(shareId);
    if (shareLink) {
      shareLink.analytics.downloadCount++;
    }
  }

  /**
   * Add comment to a shared report
   */
  static addComment(
    shareId: string,
    comment: Omit<ReportComment, 'id' | 'createdAt' | 'resolved'>
  ): ReportComment {
    const commentId = this.generateCommentId();
    const newComment: ReportComment = {
      id: commentId,
      ...comment,
      createdAt: new Date(),
      resolved: false
    };

    if (!this.comments.has(shareId)) {
      this.comments.set(shareId, []);
    }

    this.comments.get(shareId)!.push(newComment);

    // Update analytics
    const shareLink = this.shareLinks.get(shareId);
    if (shareLink) {
      shareLink.analytics.commentCount++;
    }

    return newComment;
  }

  /**
   * Get comments for a shared report
   */
  static getComments(shareId: string): ReportComment[] {
    return this.comments.get(shareId) || [];
  }

  /**
   * Reply to a comment
   */
  static replyToComment(
    shareId: string,
    parentCommentId: string,
    reply: Omit<ReportComment, 'id' | 'createdAt' | 'resolved' | 'replies'>
  ): ReportComment {
    const comments = this.comments.get(shareId) || [];
    const parentComment = comments.find(c => c.id === parentCommentId);
    
    if (!parentComment) {
      throw new Error('Parent comment not found');
    }

    const replyComment: ReportComment = {
      id: this.generateCommentId(),
      ...reply,
      createdAt: new Date(),
      resolved: false
    };

    if (!parentComment.replies) {
      parentComment.replies = [];
    }
    parentComment.replies.push(replyComment);

    return replyComment;
  }

  /**
   * Resolve a comment
   */
  static resolveComment(shareId: string, commentId: string): boolean {
    const comments = this.comments.get(shareId) || [];
    const comment = comments.find(c => c.id === commentId);
    
    if (comment) {
      comment.resolved = true;
      return true;
    }
    
    return false;
  }

  /**
   * Update share settings
   */
  static updateShareSettings(
    shareId: string,
    updates: Partial<ShareSettings>
  ): ShareLink | null {
    const shareLink = this.shareLinks.get(shareId);
    if (!shareLink) return null;

    shareLink.settings = { ...shareLink.settings, ...updates };
    return shareLink;
  }

  /**
   * Revoke share link
   */
  static revokeShareLink(shareId: string): boolean {
    return this.shareLinks.delete(shareId);
  }

  /**
   * Get all share links for a report
   */
  static getReportShareLinks(reportId: string): ShareLink[] {
    return Array.from(this.shareLinks.values())
      .filter(link => link.reportId === reportId);
  }

  /**
   * Get share analytics
   */
  static getShareAnalytics(shareId: string): ShareAnalytics | null {
    const shareLink = this.shareLinks.get(shareId);
    return shareLink ? shareLink.analytics : null;
  }

  /**
   * Send notifications to recipients
   */
  private static async notifyRecipients(
    shareLink: ShareLink,
    recipients: ShareRecipient[]
  ): Promise<void> {
    // In a real implementation, this would send emails
    console.log(`Sending share notifications for report ${shareLink.reportId} to:`, 
      recipients.map(r => r.email));
    
    // Mark recipients as notified
    recipients.forEach(recipient => {
      recipient.notified = true;
    });
  }

  /**
   * Generate secure share ID
   */
  private static generateShareId(): string {
    return `share_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Generate secure access token
   */
  private static generateSecureToken(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Generate comment ID
   */
  private static generateCommentId(): string {
    return `comment_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }
}
