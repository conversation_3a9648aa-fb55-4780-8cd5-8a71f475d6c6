/**
 * Type safety utilities for preventing "Cannot read properties of undefined" errors
 */

/**
 * Safely call a method on an object that might be undefined
 */
export function safeCall<T, A extends any[], R>(
  obj: T | undefined | null,
  method: keyof T,
  context: any,
  ...args: A
): R | undefined {
  if (obj && typeof (obj[method] as any)?.call === 'function') {
    try {
      return ((obj[method] as any).call(context, ...args) as R);
    } catch (error) {
      console.error(`Error calling ${String(method)}:`, error);
      return undefined;
    }
  }
  return undefined;
}

/**
 * Safely access a property that might be undefined
 */
export function safeGet<T, K extends keyof T>(
  obj: T | undefined | null,
  key: K
): T[K] | undefined {
  return obj?.[key];
}

/**
 * Safely access a nested property
 */
export function safeGetNested<T>(
  obj: any,
  path: string,
  defaultValue?: T
): T | undefined {
  try {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current == null) {
        return defaultValue;
      }
      current = current[key];
    }
    
    return current ?? defaultValue;
  } catch {
    return defaultValue;
  }
}

/**
 * Type guard to check if an object has a specific method
 */
export function hasMethod<T>(
  obj: any,
  method: string
): obj is T & Record<string, (...args: any[]) => any> {
  return obj && typeof obj[method] === 'function';
}

/**
 * Type guard to check if an object has a specific property
 */
export function hasProperty<T, K extends string>(
  obj: any,
  prop: K
): obj is T & Record<K, any> {
  return obj && prop in obj;
}

/**
 * Safely execute a function with error handling
 */
export function safeExecute<T>(
  fn: () => T,
  fallback?: T,
  onError?: (error: Error) => void
): T | undefined {
  try {
    return fn();
  } catch (error) {
    if (onError && error instanceof Error) {
      onError(error);
    } else {
      console.error('Safe execution error:', error);
    }
    return fallback;
  }
}

/**
 * Safely execute an async function with error handling
 */
export async function safeExecuteAsync<T>(
  fn: () => Promise<T>,
  fallback?: T,
  onError?: (error: Error) => void
): Promise<T | undefined> {
  try {
    return await fn();
  } catch (error) {
    if (onError && error instanceof Error) {
      onError(error);
    } else {
      console.error('Safe async execution error:', error);
    }
    return fallback;
  }
}

/**
 * Ensure a value is not null or undefined
 */
export function assertDefined<T>(
  value: T | null | undefined,
  message?: string
): T {
  if (value == null) {
    throw new Error(message || 'Value is null or undefined');
  }
  return value;
}

/**
 * Provide a default value if the input is null or undefined
 */
export function withDefault<T>(
  value: T | null | undefined,
  defaultValue: T
): T {
  return value ?? defaultValue;
}

/**
 * Check if a value is defined (not null or undefined)
 */
export function isDefined<T>(value: T | null | undefined): value is T {
  return value != null;
}

/**
 * Filter out null and undefined values from an array
 */
export function filterDefined<T>(
  array: (T | null | undefined)[]
): T[] {
  return array.filter(isDefined);
}

/**
 * Safely parse JSON with error handling
 */
export function safeJsonParse<T>(
  json: string,
  fallback?: T
): T | undefined {
  try {
    return JSON.parse(json);
  } catch {
    return fallback;
  }
}

/**
 * Safely stringify JSON with error handling
 */
export function safeJsonStringify(
  obj: any,
  fallback: string = '{}'
): string {
  try {
    return JSON.stringify(obj);
  } catch {
    return fallback;
  }
}

/**
 * Create a safe wrapper for event handlers
 */
export function safeEventHandler<T extends Event>(
  handler: (event: T) => void,
  onError?: (error: Error) => void
) {
  return (event: T) => {
    try {
      handler(event);
    } catch (error) {
      if (onError && error instanceof Error) {
        onError(error);
      } else {
        console.error('Event handler error:', error);
      }
    }
  };
}

/**
 * Create a debounced version of a function with safe execution
 */
export function safeDebounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  onError?: (error: Error) => void
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      try {
        func(...args);
      } catch (error) {
        if (onError && error instanceof Error) {
          onError(error);
        } else {
          console.error('Debounced function error:', error);
        }
      }
    }, wait);
  };
}

/**
 * Create a throttled version of a function with safe execution
 */
export function safeThrottle<T extends (...args: any[]) => any>(
  func: T,
  limit: number,
  onError?: (error: Error) => void
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      try {
        func(...args);
      } catch (error) {
        if (onError && error instanceof Error) {
          onError(error);
        } else {
          console.error('Throttled function error:', error);
        }
      }
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
