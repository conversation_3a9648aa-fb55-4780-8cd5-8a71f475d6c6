export interface WorkflowStep {
  id: string;
  name: string;
  type: 'condition' | 'action' | 'delay' | 'parallel' | 'loop';
  config: any;
  nextSteps: string[];
  onSuccess?: string;
  onFailure?: string;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  trigger: WorkflowTrigger;
  steps: WorkflowStep[];
  status: 'active' | 'inactive' | 'draft';
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  executionCount: number;
  successRate: number;
}

export interface WorkflowTrigger {
  type: 'manual' | 'schedule' | 'event' | 'webhook';
  config: any;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  startedAt: Date;
  completedAt?: Date;
  currentStep?: string;
  context: Record<string, any>;
  logs: WorkflowLog[];
  error?: string;
}

export interface WorkflowLog {
  timestamp: Date;
  level: 'info' | 'warn' | 'error';
  message: string;
  stepId?: string;
  data?: any;
}

export class WorkflowEngine {
  private workflows: Map<string, Workflow> = new Map();
  private executions: Map<string, WorkflowExecution> = new Map();
  private scheduledJobs: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    this.initializeDefaultWorkflows();
  }

  private initializeDefaultWorkflows() {
    // Property Alert Workflow
    this.createWorkflow({
      id: 'property-alert-workflow',
      name: 'Property Alert Automation',
      description: 'Automatically evaluate new properties and send alerts for qualified ones',
      trigger: {
        type: 'event',
        config: {
          eventType: 'property_added',
        },
      },
      steps: [
        {
          id: 'evaluate-property',
          name: 'Evaluate Property',
          type: 'action',
          config: {
            action: 'evaluate_property',
            timeout: 30000,
          },
          nextSteps: ['check-qualification'],
        },
        {
          id: 'check-qualification',
          name: 'Check if Qualified',
          type: 'condition',
          config: {
            condition: 'evaluation.passed === true && evaluation.overallScore >= 70',
          },
          onSuccess: 'send-alert',
          onFailure: 'log-rejection',
          nextSteps: [],
        },
        {
          id: 'send-alert',
          name: 'Send Property Alert',
          type: 'action',
          config: {
            action: 'send_property_alert',
            recipients: 'all_users',
          },
          nextSteps: ['generate-report'],
        },
        {
          id: 'generate-report',
          name: 'Generate Initial Report',
          type: 'action',
          config: {
            action: 'generate_report',
            reportType: 'executive-summary',
          },
          nextSteps: [],
        },
        {
          id: 'log-rejection',
          name: 'Log Rejection Reason',
          type: 'action',
          config: {
            action: 'log_message',
            message: 'Property did not meet qualification criteria',
          },
          nextSteps: [],
        },
      ],
      status: 'active',
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date(),
      executionCount: 0,
      successRate: 0,
    });

    // Weekly Market Report Workflow
    this.createWorkflow({
      id: 'weekly-market-report',
      name: 'Weekly Market Report',
      description: 'Generate and send weekly market analysis reports',
      trigger: {
        type: 'schedule',
        config: {
          cron: '0 9 * * 1', // Every Monday at 9 AM
        },
      },
      steps: [
        {
          id: 'collect-data',
          name: 'Collect Market Data',
          type: 'action',
          config: {
            action: 'collect_market_data',
            timeframe: 'week',
          },
          nextSteps: ['analyze-trends'],
        },
        {
          id: 'analyze-trends',
          name: 'Analyze Market Trends',
          type: 'action',
          config: {
            action: 'analyze_market_trends',
          },
          nextSteps: ['generate-insights'],
        },
        {
          id: 'generate-insights',
          name: 'Generate Market Insights',
          type: 'action',
          config: {
            action: 'generate_market_insights',
          },
          nextSteps: ['send-reports'],
        },
        {
          id: 'send-reports',
          name: 'Send Weekly Reports',
          type: 'action',
          config: {
            action: 'send_weekly_digest',
            recipients: 'subscribed_users',
          },
          nextSteps: [],
        },
      ],
      status: 'active',
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date(),
      executionCount: 0,
      successRate: 0,
    });

    // Data Sync Workflow
    this.createWorkflow({
      id: 'data-sync-workflow',
      name: 'Data Synchronization',
      description: 'Sync data from external sources and update property information',
      trigger: {
        type: 'schedule',
        config: {
          cron: '0 2 * * *', // Every day at 2 AM
        },
      },
      steps: [
        {
          id: 'sync-assessor-data',
          name: 'Sync Assessor Data',
          type: 'action',
          config: {
            action: 'sync_data_source',
            source: 'county_assessor',
          },
          nextSteps: ['sync-zoning-data'],
        },
        {
          id: 'sync-zoning-data',
          name: 'Sync Zoning Data',
          type: 'action',
          config: {
            action: 'sync_data_source',
            source: 'zoning_department',
          },
          nextSteps: ['sync-transit-data'],
        },
        {
          id: 'sync-transit-data',
          name: 'Sync Transit Data',
          type: 'action',
          config: {
            action: 'sync_data_source',
            source: 'transit_authority',
          },
          nextSteps: ['validate-data'],
        },
        {
          id: 'validate-data',
          name: 'Validate Data Quality',
          type: 'action',
          config: {
            action: 'validate_data_quality',
          },
          nextSteps: ['update-evaluations'],
        },
        {
          id: 'update-evaluations',
          name: 'Update Property Evaluations',
          type: 'action',
          config: {
            action: 'batch_evaluate_properties',
            batchSize: 100,
          },
          nextSteps: [],
        },
      ],
      status: 'active',
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date(),
      executionCount: 0,
      successRate: 0,
    });
  }

  createWorkflow(workflow: Omit<Workflow, 'id'> & { id?: string }): string {
    const id = workflow.id || `workflow_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const fullWorkflow: Workflow = {
      ...workflow,
      id,
    };

    this.workflows.set(id, fullWorkflow);

    // Set up scheduled trigger if applicable
    if (fullWorkflow.trigger.type === 'schedule' && fullWorkflow.status === 'active') {
      this.scheduleWorkflow(fullWorkflow);
    }

    return id;
  }

  private scheduleWorkflow(workflow: Workflow) {
    if (workflow.trigger.type !== 'schedule') return;

    const { cron } = workflow.trigger.config;
    
    // Simple cron parsing for demo (in production, use a proper cron library)
    const cronParts = cron.split(' ');
    if (cronParts.length === 5) {
      const [minute, hour, dayOfMonth, month, dayOfWeek] = cronParts;
      
      // For demo, just schedule to run every hour
      const interval = setInterval(() => {
        this.executeWorkflow(workflow.id, {});
      }, 60 * 60 * 1000); // 1 hour
      
      this.scheduledJobs.set(workflow.id, interval);
    }
  }

  async executeWorkflow(workflowId: string, context: Record<string, any> = {}): Promise<string> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    if (workflow.status !== 'active') {
      throw new Error(`Workflow ${workflowId} is not active`);
    }

    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const execution: WorkflowExecution = {
      id: executionId,
      workflowId,
      status: 'running',
      startedAt: new Date(),
      context,
      logs: [],
    };

    this.executions.set(executionId, execution);

    try {
      await this.runWorkflowSteps(execution, workflow);
      execution.status = 'completed';
      execution.completedAt = new Date();
      
      // Update workflow statistics
      workflow.executionCount++;
      workflow.successRate = this.calculateSuccessRate(workflowId);
      
    } catch (error) {
      execution.status = 'failed';
      execution.error = (error as Error).message;
      execution.completedAt = new Date();
      
      this.addLog(execution, 'error', `Workflow execution failed: ${(error as Error).message}`);
    }

    return executionId;
  }

  private async runWorkflowSteps(execution: WorkflowExecution, workflow: Workflow) {
    const firstStep = workflow.steps[0];
    if (!firstStep) return;

    await this.executeStep(execution, workflow, firstStep);
  }

  private async executeStep(execution: WorkflowExecution, workflow: Workflow, step: WorkflowStep) {
    execution.currentStep = step.id;
    this.addLog(execution, 'info', `Executing step: ${step.name}`);

    try {
      switch (step.type) {
        case 'action':
          await this.executeAction(execution, step);
          break;
        case 'condition':
          await this.executeCondition(execution, workflow, step);
          return; // Condition handles next steps
        case 'delay':
          await this.executeDelay(execution, step);
          break;
        case 'parallel':
          await this.executeParallel(execution, workflow, step);
          break;
        case 'loop':
          await this.executeLoop(execution, workflow, step);
          break;
      }

      // Execute next steps
      for (const nextStepId of step.nextSteps) {
        const nextStep = workflow.steps.find(s => s.id === nextStepId);
        if (nextStep) {
          await this.executeStep(execution, workflow, nextStep);
        }
      }

    } catch (error) {
      this.addLog(execution, 'error', `Step ${step.name} failed: ${(error as Error).message}`);
      
      if (step.onFailure) {
        const failureStep = workflow.steps.find(s => s.id === step.onFailure);
        if (failureStep) {
          await this.executeStep(execution, workflow, failureStep);
        }
      } else {
        throw error;
      }
    }
  }

  private async executeAction(execution: WorkflowExecution, step: WorkflowStep) {
    const { action } = step.config;
    
    switch (action) {
      case 'evaluate_property':
        await this.actionEvaluateProperty(execution, step.config);
        break;
      case 'send_property_alert':
        await this.actionSendPropertyAlert(execution, step.config);
        break;
      case 'generate_report':
        await this.actionGenerateReport(execution, step.config);
        break;
      case 'sync_data_source':
        await this.actionSyncDataSource(execution, step.config);
        break;
      case 'log_message':
        this.addLog(execution, 'info', step.config.message);
        break;
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  }

  private async executeCondition(execution: WorkflowExecution, workflow: Workflow, step: WorkflowStep) {
    const { condition } = step.config;
    const result = this.evaluateCondition(condition, execution.context);
    
    this.addLog(execution, 'info', `Condition result: ${result}`);
    
    const nextStepId = result ? step.onSuccess : step.onFailure;
    if (nextStepId) {
      const nextStep = workflow.steps.find(s => s.id === nextStepId);
      if (nextStep) {
        await this.executeStep(execution, workflow, nextStep);
      }
    }
  }

  private async executeDelay(execution: WorkflowExecution, step: WorkflowStep) {
    const { duration } = step.config;
    this.addLog(execution, 'info', `Delaying for ${duration}ms`);
    await new Promise(resolve => setTimeout(resolve, duration));
  }

  private async executeParallel(execution: WorkflowExecution, workflow: Workflow, step: WorkflowStep) {
    const { steps: parallelStepIds } = step.config;
    const parallelSteps = workflow.steps.filter(s => parallelStepIds.includes(s.id));
    
    await Promise.all(
      parallelSteps.map(parallelStep => 
        this.executeStep(execution, workflow, parallelStep)
      )
    );
  }

  private async executeLoop(execution: WorkflowExecution, workflow: Workflow, step: WorkflowStep) {
    const { condition, steps: loopStepIds, maxIterations = 100 } = step.config;
    let iterations = 0;
    
    while (this.evaluateCondition(condition, execution.context) && iterations < maxIterations) {
      const loopSteps = workflow.steps.filter(s => loopStepIds.includes(s.id));
      
      for (const loopStep of loopSteps) {
        await this.executeStep(execution, workflow, loopStep);
      }
      
      iterations++;
    }
  }

  private evaluateCondition(condition: string, context: Record<string, any>): boolean {
    try {
      // Simple condition evaluation (in production, use a proper expression evaluator)
      const func = new Function('context', `with(context) { return ${condition}; }`);
      return func(context);
    } catch (error) {
      console.error('Condition evaluation error:', error);
      return false;
    }
  }

  // Action implementations
  private async actionEvaluateProperty(execution: WorkflowExecution, config: any) {
    // Mock property evaluation
    const propertyId = execution.context.propertyId;
    this.addLog(execution, 'info', `Evaluating property ${propertyId}`);
    
    // Simulate evaluation
    const evaluation = {
      passed: Math.random() > 0.3,
      overallScore: Math.floor(Math.random() * 100),
    };
    
    execution.context.evaluation = evaluation;
  }

  private async actionSendPropertyAlert(execution: WorkflowExecution, config: any) {
    this.addLog(execution, 'info', `Sending property alert to ${config.recipients}`);
    // In production, integrate with notification service
  }

  private async actionGenerateReport(execution: WorkflowExecution, config: any) {
    this.addLog(execution, 'info', `Generating ${config.reportType} report`);
    // In production, integrate with report generator
  }

  private async actionSyncDataSource(execution: WorkflowExecution, config: any) {
    this.addLog(execution, 'info', `Syncing data from ${config.source}`);
    // In production, integrate with data integration service
  }

  private addLog(execution: WorkflowExecution, level: WorkflowLog['level'], message: string, data?: any) {
    execution.logs.push({
      timestamp: new Date(),
      level,
      message,
      ...(execution.currentStep && { stepId: execution.currentStep }),
      data,
    });
  }

  private calculateSuccessRate(workflowId: string): number {
    const executions = Array.from(this.executions.values())
      .filter(e => e.workflowId === workflowId && e.status !== 'running');
    
    if (executions.length === 0) return 0;
    
    const successful = executions.filter(e => e.status === 'completed').length;
    return (successful / executions.length) * 100;
  }

  getWorkflow(id: string): Workflow | undefined {
    return this.workflows.get(id);
  }

  getAllWorkflows(): Workflow[] {
    return Array.from(this.workflows.values());
  }

  getExecution(id: string): WorkflowExecution | undefined {
    return this.executions.get(id);
  }

  getWorkflowExecutions(workflowId: string): WorkflowExecution[] {
    return Array.from(this.executions.values())
      .filter(e => e.workflowId === workflowId)
      .sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime());
  }

  async cancelExecution(executionId: string): Promise<boolean> {
    const execution = this.executions.get(executionId);
    if (execution && execution.status === 'running') {
      execution.status = 'cancelled';
      execution.completedAt = new Date();
      this.addLog(execution, 'info', 'Execution cancelled by user');
      return true;
    }
    return false;
  }

  updateWorkflowStatus(workflowId: string, status: Workflow['status']): boolean {
    const workflow = this.workflows.get(workflowId);
    if (workflow) {
      workflow.status = status;
      workflow.updatedAt = new Date();
      
      // Handle scheduling
      if (status === 'active' && workflow.trigger.type === 'schedule') {
        this.scheduleWorkflow(workflow);
      } else if (status !== 'active') {
        const job = this.scheduledJobs.get(workflowId);
        if (job) {
          clearInterval(job);
          this.scheduledJobs.delete(workflowId);
        }
      }
      
      return true;
    }
    return false;
  }

  // Trigger workflow by event
  async triggerByEvent(eventType: string, eventData: any): Promise<string[]> {
    const triggeredWorkflows = Array.from(this.workflows.values())
      .filter(w => 
        w.status === 'active' && 
        w.trigger.type === 'event' && 
        w.trigger.config.eventType === eventType
      );

    const executionIds: string[] = [];
    
    for (const workflow of triggeredWorkflows) {
      try {
        const executionId = await this.executeWorkflow(workflow.id, eventData);
        executionIds.push(executionId);
      } catch (error) {
        console.error(`Failed to execute workflow ${workflow.id}:`, error);
      }
    }

    return executionIds;
  }
}
