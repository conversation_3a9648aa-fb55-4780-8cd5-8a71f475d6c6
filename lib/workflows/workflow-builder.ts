export interface WorkflowNode {
  id: string;
  type: 'trigger' | 'condition' | 'action' | 'delay';
  name: string;
  description: string;
  config: Record<string, any>;
  position: { x: number; y: number };
  connections: string[]; // IDs of connected nodes
}

export interface WorkflowTrigger extends WorkflowNode {
  type: 'trigger';
  triggerType: 'property_added' | 'property_evaluated' | 'report_generated' | 'schedule' | 'manual';
  conditions?: WorkflowCondition[];
}

export interface WorkflowCondition extends WorkflowNode {
  type: 'condition';
  conditionType: 'property_score' | 'property_value' | 'location' | 'zoning' | 'custom';
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'in_range';
  value: any;
  trueConnection?: string;
  falseConnection?: string;
}

export interface WorkflowAction extends WorkflowNode {
  type: 'action';
  actionType: 'send_notification' | 'generate_report' | 'update_property' | 'webhook' | 'email';
  parameters: Record<string, any>;
}

export interface WorkflowDelay extends WorkflowNode {
  type: 'delay';
  duration: number; // in minutes
  unit: 'minutes' | 'hours' | 'days';
}

export interface CustomWorkflow {
  id: string;
  name: string;
  description: string;
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  status: 'active' | 'inactive' | 'draft';
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  executionCount: number;
  lastExecuted?: Date;
}

export interface WorkflowConnection {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  condition?: string; // For conditional connections
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: 'property_alerts' | 'reporting' | 'data_sync' | 'notifications';
  nodes: Omit<WorkflowNode, 'id' | 'position'>[];
  connections: Omit<WorkflowConnection, 'id'>[];
  variables: Array<{
    name: string;
    type: 'string' | 'number' | 'boolean' | 'select';
    description: string;
    required: boolean;
    options?: string[];
  }>;
}

export class WorkflowBuilder {
  private workflows: Map<string, CustomWorkflow> = new Map();
  private templates: Map<string, WorkflowTemplate> = new Map();
  private nodeTypes: Map<string, any> = new Map();

  constructor() {
    this.initializeNodeTypes();
    this.initializeTemplates();
  }

  /**
   * Create a new workflow
   */
  createWorkflow(
    name: string,
    description: string,
    userId: string
  ): CustomWorkflow {
    const workflow: CustomWorkflow = {
      id: this.generateWorkflowId(),
      name,
      description,
      nodes: [],
      connections: [],
      status: 'draft',
      createdBy: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
      executionCount: 0
    };

    this.workflows.set(workflow.id, workflow);
    return workflow;
  }

  /**
   * Create workflow from template
   */
  createFromTemplate(
    templateId: string,
    name: string,
    variables: Record<string, any>,
    userId: string
  ): CustomWorkflow {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    const workflow = this.createWorkflow(name, template.description, userId);

    // Create nodes from template
    const nodeIdMap: Map<string, string> = new Map();
    
    template.nodes.forEach((templateNode, index) => {
      const nodeId = this.generateNodeId();
      nodeIdMap.set(index.toString(), nodeId);

      const node: WorkflowNode = {
        id: nodeId,
        ...templateNode,
        position: { x: index * 200, y: 100 },
        connections: []
      };

      // Replace variables in config
      node.config = this.replaceVariables(node.config, variables);
      
      workflow.nodes.push(node);
    });

    // Create connections from template
    template.connections.forEach(templateConnection => {
      const sourceNodeId = nodeIdMap.get(templateConnection.sourceNodeId);
      const targetNodeId = nodeIdMap.get(templateConnection.targetNodeId);

      if (sourceNodeId && targetNodeId) {
        const connection: WorkflowConnection = {
          id: this.generateConnectionId(),
          sourceNodeId,
          targetNodeId,
          ...(templateConnection.condition && { condition: templateConnection.condition })
        };

        workflow.connections.push(connection);

        // Update node connections
        const sourceNode = workflow.nodes.find(n => n.id === sourceNodeId);
        if (sourceNode) {
          sourceNode.connections.push(targetNodeId);
        }
      }
    });

    this.workflows.set(workflow.id, workflow);
    return workflow;
  }

  /**
   * Add node to workflow
   */
  addNode(
    workflowId: string,
    nodeType: WorkflowNode['type'],
    config: Record<string, any>,
    position: { x: number; y: number }
  ): WorkflowNode {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`);
    }

    const nodeTemplate = this.nodeTypes.get(nodeType);
    if (!nodeTemplate) {
      throw new Error(`Node type not found: ${nodeType}`);
    }

    const node: WorkflowNode = {
      id: this.generateNodeId(),
      type: nodeType,
      name: config.name || nodeTemplate.defaultName,
      description: config.description || nodeTemplate.description,
      config,
      position,
      connections: []
    };

    workflow.nodes.push(node);
    workflow.updatedAt = new Date();

    return node;
  }

  /**
   * Connect two nodes
   */
  connectNodes(
    workflowId: string,
    sourceNodeId: string,
    targetNodeId: string,
    condition?: string
  ): WorkflowConnection {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`);
    }

    const sourceNode = workflow.nodes.find(n => n.id === sourceNodeId);
    const targetNode = workflow.nodes.find(n => n.id === targetNodeId);

    if (!sourceNode || !targetNode) {
      throw new Error('Source or target node not found');
    }

    const connection: WorkflowConnection = {
      id: this.generateConnectionId(),
      sourceNodeId,
      targetNodeId,
      ...(condition && { condition })
    };

    workflow.connections.push(connection);
    sourceNode.connections.push(targetNodeId);
    workflow.updatedAt = new Date();

    return connection;
  }

  /**
   * Remove node from workflow
   */
  removeNode(workflowId: string, nodeId: string): boolean {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return false;

    // Remove node
    const nodeIndex = workflow.nodes.findIndex(n => n.id === nodeId);
    if (nodeIndex === -1) return false;

    workflow.nodes.splice(nodeIndex, 1);

    // Remove connections involving this node
    workflow.connections = workflow.connections.filter(
      c => c.sourceNodeId !== nodeId && c.targetNodeId !== nodeId
    );

    // Update other nodes' connections
    workflow.nodes.forEach(node => {
      node.connections = node.connections.filter(id => id !== nodeId);
    });

    workflow.updatedAt = new Date();
    return true;
  }

  /**
   * Update node configuration
   */
  updateNode(
    workflowId: string,
    nodeId: string,
    updates: Partial<WorkflowNode>
  ): boolean {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return false;

    const node = workflow.nodes.find(n => n.id === nodeId);
    if (!node) return false;

    Object.assign(node, updates);
    workflow.updatedAt = new Date();

    return true;
  }

  /**
   * Validate workflow
   */
  validateWorkflow(workflowId: string): { valid: boolean; errors: string[] } {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      return { valid: false, errors: ['Workflow not found'] };
    }

    const errors: string[] = [];

    // Check for trigger nodes
    const triggerNodes = workflow.nodes.filter(n => n.type === 'trigger');
    if (triggerNodes.length === 0) {
      errors.push('Workflow must have at least one trigger node');
    }

    // Check for orphaned nodes
    const connectedNodeIds = new Set<string>();
    workflow.connections.forEach(conn => {
      connectedNodeIds.add(conn.sourceNodeId);
      connectedNodeIds.add(conn.targetNodeId);
    });

    const orphanedNodes = workflow.nodes.filter(
      node => node.type !== 'trigger' && !connectedNodeIds.has(node.id)
    );

    if (orphanedNodes.length > 0) {
      errors.push(`Found ${orphanedNodes.length} orphaned nodes`);
    }

    // Check for circular dependencies
    if (this.hasCircularDependency(workflow)) {
      errors.push('Workflow contains circular dependencies');
    }

    // Validate node configurations
    workflow.nodes.forEach(node => {
      const nodeErrors = this.validateNodeConfig(node);
      errors.push(...nodeErrors);
    });

    return { valid: errors.length === 0, errors };
  }

  /**
   * Activate workflow
   */
  activateWorkflow(workflowId: string): boolean {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return false;

    const validation = this.validateWorkflow(workflowId);
    if (!validation.valid) {
      throw new Error(`Cannot activate invalid workflow: ${validation.errors.join(', ')}`);
    }

    workflow.status = 'active';
    workflow.updatedAt = new Date();

    return true;
  }

  /**
   * Deactivate workflow
   */
  deactivateWorkflow(workflowId: string): boolean {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return false;

    workflow.status = 'inactive';
    workflow.updatedAt = new Date();

    return true;
  }

  /**
   * Get workflow
   */
  getWorkflow(workflowId: string): CustomWorkflow | null {
    return this.workflows.get(workflowId) || null;
  }

  /**
   * Get all workflows for user
   */
  getUserWorkflows(userId: string): CustomWorkflow[] {
    return Array.from(this.workflows.values())
      .filter(workflow => workflow.createdBy === userId);
  }

  /**
   * Get available templates
   */
  getTemplates(): WorkflowTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Get available node types
   */
  getNodeTypes(): Array<{ type: string; name: string; description: string; config: any }> {
    return Array.from(this.nodeTypes.entries()).map(([type, config]) => ({
      type,
      name: config.name,
      description: config.description,
      config: config.configSchema
    }));
  }

  /**
   * Private helper methods
   */
  private initializeNodeTypes(): void {
    this.nodeTypes.set('trigger', {
      name: 'Trigger',
      description: 'Starts the workflow execution',
      defaultName: 'Workflow Trigger',
      configSchema: {
        triggerType: { type: 'select', options: ['property_added', 'property_evaluated', 'schedule'] },
        conditions: { type: 'array', optional: true }
      }
    });

    this.nodeTypes.set('condition', {
      name: 'Condition',
      description: 'Evaluates a condition and branches execution',
      defaultName: 'Condition Check',
      configSchema: {
        conditionType: { type: 'select', options: ['property_score', 'property_value', 'location'] },
        operator: { type: 'select', options: ['equals', 'greater_than', 'less_than'] },
        value: { type: 'any' }
      }
    });

    this.nodeTypes.set('action', {
      name: 'Action',
      description: 'Performs an action',
      defaultName: 'Action',
      configSchema: {
        actionType: { type: 'select', options: ['send_notification', 'generate_report', 'webhook'] },
        parameters: { type: 'object' }
      }
    });

    this.nodeTypes.set('delay', {
      name: 'Delay',
      description: 'Waits for a specified duration',
      defaultName: 'Delay',
      configSchema: {
        duration: { type: 'number', min: 1 },
        unit: { type: 'select', options: ['minutes', 'hours', 'days'] }
      }
    });
  }

  private initializeTemplates(): void {
    // Property Alert Template
    this.templates.set('property-alert', {
      id: 'property-alert',
      name: 'Property Alert Workflow',
      description: 'Automatically send alerts when qualified properties are found',
      category: 'property_alerts',
      nodes: [
        {
          type: 'trigger',
          name: 'Property Added',
          description: 'Triggers when a new property is added',
          config: { triggerType: 'property_added' },
          connections: []
        },
        {
          type: 'condition',
          name: 'Check Score',
          description: 'Check if property score meets threshold',
          config: {
            conditionType: 'property_score',
            operator: 'greater_than',
            value: '{{min_score}}'
          },
          connections: []
        },
        {
          type: 'action',
          name: 'Send Alert',
          description: 'Send notification to user',
          config: {
            actionType: 'send_notification',
            parameters: {
              type: 'property_alert',
              channels: ['email', 'push']
            }
          },
          connections: []
        }
      ],
      connections: [
        { sourceNodeId: '0', targetNodeId: '1' },
        { sourceNodeId: '1', targetNodeId: '2', condition: 'true' }
      ],
      variables: [
        {
          name: 'min_score',
          type: 'number',
          description: 'Minimum property score to trigger alert',
          required: true
        }
      ]
    });

    // Weekly Report Template
    this.templates.set('weekly-report', {
      id: 'weekly-report',
      name: 'Weekly Report Generation',
      description: 'Generate and send weekly property reports',
      category: 'reporting',
      nodes: [
        {
          type: 'trigger',
          name: 'Weekly Schedule',
          description: 'Triggers every week',
          config: {
            triggerType: 'schedule',
            schedule: 'weekly',
            day: '{{report_day}}',
            time: '{{report_time}}'
          },
          connections: []
        },
        {
          type: 'action',
          name: 'Generate Report',
          description: 'Generate weekly market report',
          config: {
            actionType: 'generate_report',
            parameters: {
              reportType: 'weekly_market_summary',
              includeCharts: true
            }
          },
          connections: []
        },
        {
          type: 'action',
          name: 'Send Report',
          description: 'Email report to subscribers',
          config: {
            actionType: 'email',
            parameters: {
              template: 'weekly_report',
              recipients: '{{recipients}}'
            }
          },
          connections: []
        }
      ],
      connections: [
        { sourceNodeId: '0', targetNodeId: '1' },
        { sourceNodeId: '1', targetNodeId: '2' }
      ],
      variables: [
        {
          name: 'report_day',
          type: 'select',
          description: 'Day of the week to send report',
          required: true,
          options: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
        },
        {
          name: 'report_time',
          type: 'string',
          description: 'Time to send report (HH:MM)',
          required: true
        },
        {
          name: 'recipients',
          type: 'string',
          description: 'Comma-separated email addresses',
          required: true
        }
      ]
    });
  }

  private replaceVariables(config: any, variables: Record<string, any>): any {
    const configStr = JSON.stringify(config);
    let replacedStr = configStr;

    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      replacedStr = replacedStr.replace(new RegExp(placeholder, 'g'), String(value));
    });

    return JSON.parse(replacedStr);
  }

  private hasCircularDependency(workflow: CustomWorkflow): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) return true;
      if (visited.has(nodeId)) return false;

      visited.add(nodeId);
      recursionStack.add(nodeId);

      const node = workflow.nodes.find(n => n.id === nodeId);
      if (node) {
        for (const connectedId of node.connections) {
          if (hasCycle(connectedId)) return true;
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const node of workflow.nodes) {
      if (!visited.has(node.id) && hasCycle(node.id)) {
        return true;
      }
    }

    return false;
  }

  private validateNodeConfig(node: WorkflowNode): string[] {
    const errors: string[] = [];
    const nodeType = this.nodeTypes.get(node.type);

    if (!nodeType) {
      errors.push(`Invalid node type: ${node.type}`);
      return errors;
    }

    // Validate required fields based on node type
    if (node.type === 'trigger' && !node.config.triggerType) {
      errors.push(`Trigger node ${node.id} missing triggerType`);
    }

    if (node.type === 'condition') {
      if (!node.config.conditionType || !node.config.operator || node.config.value === undefined) {
        errors.push(`Condition node ${node.id} missing required configuration`);
      }
    }

    if (node.type === 'action' && !node.config.actionType) {
      errors.push(`Action node ${node.id} missing actionType`);
    }

    if (node.type === 'delay' && (!node.config.duration || !node.config.unit)) {
      errors.push(`Delay node ${node.id} missing duration or unit`);
    }

    return errors;
  }

  private generateWorkflowId(): string {
    return `workflow_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private generateNodeId(): string {
    return `node_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }

  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }
}
