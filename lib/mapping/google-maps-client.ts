export interface Coordinates {
  lat: number;
  lng: number;
}

export interface PlaceDetails {
  placeId: string;
  name: string;
  address: string;
  coordinates: Coordinates;
  types: string[];
  rating?: number;
  priceLevel?: number;
}

export interface TransitStop {
  id: string;
  name: string;
  coordinates: Coordinates;
  type: 'bus' | 'rail' | 'subway' | 'light_rail';
  routes: Array<{
    id: string;
    name: string;
    type: string;
  }>;
}

export interface DistanceMatrixResult {
  origin: Coordinates;
  destination: Coordinates;
  distance: {
    text: string;
    value: number; // in meters
  };
  duration: {
    text: string;
    value: number; // in seconds
  };
  mode: 'driving' | 'walking' | 'transit' | 'bicycling';
}

export interface GeocodingResult {
  address: string;
  coordinates: Coordinates;
  formattedAddress: string;
  addressComponents: Array<{
    longName: string;
    shortName: string;
    types: string[];
  }>;
  placeId: string;
}

export class GoogleMapsClient {
  private apiKey: string;
  private baseUrl = 'https://maps.googleapis.com/maps/api';

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.GOOGLE_MAPS_API_KEY || '';
    if (!this.apiKey) {
      throw new Error('Google Maps API key is required');
    }
  }

  /**
   * Geocode an address to coordinates
   */
  async geocodeAddress(address: string): Promise<GeocodingResult> {
    const url = `${this.baseUrl}/geocode/json`;
    const params = new URLSearchParams({
      address,
      key: this.apiKey,
    });

    try {
      const response = await fetch(`${url}?${params}`);
      const data = await response.json();

      if (data.status !== 'OK' || !data.results.length) {
        throw new Error(`Geocoding failed: ${data.status}`);
      }

      const result = data.results[0];
      const location = result.geometry.location;

      return {
        address,
        coordinates: {
          lat: location.lat,
          lng: location.lng,
        },
        formattedAddress: result.formatted_address,
        addressComponents: result.address_components,
        placeId: result.place_id,
      };
    } catch (error) {
      console.error('Geocoding error:', error);
      throw new Error(`Failed to geocode address: ${(error as Error).message}`);
    }
  }

  /**
   * Reverse geocode coordinates to address
   */
  async reverseGeocode(coordinates: Coordinates): Promise<GeocodingResult> {
    const url = `${this.baseUrl}/geocode/json`;
    const params = new URLSearchParams({
      latlng: `${coordinates.lat},${coordinates.lng}`,
      key: this.apiKey,
    });

    try {
      const response = await fetch(`${url}?${params}`);
      const data = await response.json();

      if (data.status !== 'OK' || !data.results.length) {
        throw new Error(`Reverse geocoding failed: ${data.status}`);
      }

      const result = data.results[0];

      return {
        address: result.formatted_address,
        coordinates,
        formattedAddress: result.formatted_address,
        addressComponents: result.address_components,
        placeId: result.place_id,
      };
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      throw new Error(`Failed to reverse geocode: ${(error as Error).message}`);
    }
  }

  /**
   * Find nearby places of a specific type
   */
  async findNearbyPlaces(
    coordinates: Coordinates,
    type: string,
    radius: number = 1000
  ): Promise<PlaceDetails[]> {
    const url = `${this.baseUrl}/place/nearbysearch/json`;
    const params = new URLSearchParams({
      location: `${coordinates.lat},${coordinates.lng}`,
      radius: radius.toString(),
      type,
      key: this.apiKey,
    });

    try {
      const response = await fetch(`${url}?${params}`);
      const data = await response.json();

      if (data.status !== 'OK') {
        throw new Error(`Places search failed: ${data.status}`);
      }

      return data.results.map((place: any) => ({
        placeId: place.place_id,
        name: place.name,
        address: place.vicinity,
        coordinates: {
          lat: place.geometry.location.lat,
          lng: place.geometry.location.lng,
        },
        types: place.types,
        rating: place.rating,
        priceLevel: place.price_level,
      }));
    } catch (error) {
      console.error('Places search error:', error);
      throw new Error(`Failed to find nearby places: ${(error as Error).message}`);
    }
  }

  /**
   * Find transit stops near a location
   */
  async findNearbyTransitStops(
    coordinates: Coordinates,
    radius: number = 800
  ): Promise<TransitStop[]> {
    const transitTypes = ['bus_station', 'subway_station', 'train_station', 'light_rail_station'];
    const allStops: TransitStop[] = [];

    for (const type of transitTypes) {
      try {
        const places = await this.findNearbyPlaces(coordinates, type, radius);
        
        for (const place of places) {
          const stop: TransitStop = {
            id: place.placeId,
            name: place.name,
            coordinates: place.coordinates,
            type: this.mapPlaceTypeToTransitType(type),
            routes: [], // Would need additional API calls to get route details
          };
          allStops.push(stop);
        }
      } catch (error) {
        console.warn(`Failed to find ${type} stops:`, error);
      }
    }

    return allStops;
  }

  /**
   * Calculate distance matrix between origins and destinations
   */
  async calculateDistanceMatrix(
    origins: Coordinates[],
    destinations: Coordinates[],
    mode: 'driving' | 'walking' | 'transit' | 'bicycling' = 'driving'
  ): Promise<DistanceMatrixResult[]> {
    const url = `${this.baseUrl}/distancematrix/json`;
    
    const originsStr = origins.map(coord => `${coord.lat},${coord.lng}`).join('|');
    const destinationsStr = destinations.map(coord => `${coord.lat},${coord.lng}`).join('|');
    
    const params = new URLSearchParams({
      origins: originsStr,
      destinations: destinationsStr,
      mode,
      units: 'metric',
      key: this.apiKey,
    });

    try {
      const response = await fetch(`${url}?${params}`);
      const data = await response.json();

      if (data.status !== 'OK') {
        throw new Error(`Distance matrix failed: ${data.status}`);
      }

      const results: DistanceMatrixResult[] = [];
      
      data.rows.forEach((row: any, originIndex: number) => {
        row.elements.forEach((element: any, destIndex: number) => {
          if (element.status === 'OK') {
            const origin = origins[originIndex];
            const destination = destinations[destIndex];

            if (origin && destination) {
              results.push({
                origin,
                destination,
                distance: element.distance,
                duration: element.duration,
                mode,
              });
            }
          }
        });
      });

      return results;
    } catch (error) {
      console.error('Distance matrix error:', error);
      throw new Error(`Failed to calculate distance matrix: ${(error as Error).message}`);
    }
  }

  /**
   * Get elevation data for coordinates
   */
  async getElevation(coordinates: Coordinates[]): Promise<Array<{ location: Coordinates; elevation: number }>> {
    const url = `${this.baseUrl}/elevation/json`;
    
    const locationsStr = coordinates.map(coord => `${coord.lat},${coord.lng}`).join('|');
    
    const params = new URLSearchParams({
      locations: locationsStr,
      key: this.apiKey,
    });

    try {
      const response = await fetch(`${url}?${params}`);
      const data = await response.json();

      if (data.status !== 'OK') {
        throw new Error(`Elevation API failed: ${data.status}`);
      }

      return data.results.map((result: any) => ({
        location: {
          lat: result.location.lat,
          lng: result.location.lng,
        },
        elevation: result.elevation,
      }));
    } catch (error) {
      console.error('Elevation API error:', error);
      throw new Error(`Failed to get elevation data: ${(error as Error).message}`);
    }
  }

  /**
   * Calculate walking time to transit stops
   */
  async calculateTransitAccessibility(
    propertyCoordinates: Coordinates,
    transitStops: TransitStop[]
  ): Promise<Array<{ stop: TransitStop; walkingTime: number; distance: number }>> {
    if (transitStops.length === 0) return [];

    const destinations = transitStops.map(stop => stop.coordinates);
    
    try {
      const distanceResults = await this.calculateDistanceMatrix(
        [propertyCoordinates],
        destinations,
        'walking'
      );

      return distanceResults.map((result, index) => {
        const stop = transitStops[index];
        if (!stop) {
          throw new Error(`Transit stop at index ${index} is undefined`);
        }

        return {
          stop,
          walkingTime: result.duration.value, // in seconds
          distance: result.distance.value, // in meters
        };
      });
    } catch (error) {
      console.error('Transit accessibility calculation error:', error);
      return [];
    }
  }

  /**
   * Calculate property boundaries and area
   */
  calculatePropertyArea(coordinates: Coordinates[]): number {
    if (coordinates.length < 3) return 0;

    // Use the shoelace formula to calculate polygon area
    let area = 0;
    const n = coordinates.length;

    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      const currentCoord = coordinates[i];
      const nextCoord = coordinates[j];

      if (currentCoord && nextCoord) {
        area += currentCoord.lat * nextCoord.lng;
        area -= nextCoord.lat * currentCoord.lng;
      }
    }

    area = Math.abs(area) / 2;

    // Convert from degrees to square meters (approximate)
    const metersPerDegree = 111320; // at equator
    return area * metersPerDegree * metersPerDegree;
  }

  /**
   * Calculate distance between two coordinates
   */
  calculateDistance(coord1: Coordinates, coord2: Coordinates): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (coord1.lat * Math.PI) / 180;
    const φ2 = (coord2.lat * Math.PI) / 180;
    const Δφ = ((coord2.lat - coord1.lat) * Math.PI) / 180;
    const Δλ = ((coord2.lng - coord1.lng) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
  }

  private mapPlaceTypeToTransitType(placeType: string): TransitStop['type'] {
    const mapping: { [key: string]: TransitStop['type'] } = {
      bus_station: 'bus',
      subway_station: 'subway',
      train_station: 'rail',
      light_rail_station: 'light_rail',
    };
    
    return mapping[placeType] || 'bus';
  }
}
